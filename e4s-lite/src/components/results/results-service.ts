import type {
  AthleteTrialMap,
  AthleteTrialMapAbnormal,
  CompetitionOnTheDay,
  CompetitionOptions,
  CompetitionScheduleOptions,
  CompEvent,
  FollowingEvents,
  HeatNoString,
  IndividualEntry,
  IndividualResult,
  Pof10,
  QualifyLinks,
  ScoreNeedsCleaning,
  ShouldFollowEvent,
  Team,
  TrialNo
} from '@/components/results/store-comp-results'
import {
  fixDateIsoIsh,
  formatDateWithSuffix,
  getDatePartFromIsoString,
  getTimePartFromIsoString,
  isNumeric,
  simpleClone,
  sortArray
} from '@/services/common-service-utils'
import {
  type EventGroupIdString,
  type EventType,
  EventTypeDictionary,
  type EventTypeMap,
  type IsoDate
} from '@/common/common-models'
import type { DateIsoIsh, EventGroupId } from '@/services/common'
import type {
  LiveFeedMessageBase,
  LiveFeedMessageFieldResult,
  LiveFeedMessageTrackResult
} from '@/components/results/socket/live-feed/live-feed-models'
import type {
  ApiCompetitionSchedule,
  ApiCompetitionScheduleOptions
} from '@/components/results/middleware/competition-converter.models'

export function sortSchedule(compEvents: CompEvent[], propName: keyof CompEvent): CompEvent[] {
  // return sortArray(propName, compEvents, 'ASC')
  // need to sort by eventDate then by typeNo
  return compEvents.sort((a, b) => {
    const aDate = a.eventDate
    const bDate = b.eventDate
    if (aDate < bDate) {
      return -1
    }
    if (aDate > bDate) {
      return 1
    }

    // Sort typeNo by prefix (F/T) then by numeric value
    const aPrefix = a.typeNo.charAt(0)
    const bPrefix = b.typeNo.charAt(0)
    const aNumber = parseInt(a.typeNo.slice(1))
    const bNumber = parseInt(b.typeNo.slice(1))

    if (aPrefix !== bPrefix) {
      return aPrefix.localeCompare(bPrefix)
    }
    return aNumber - bNumber
  })
}

// Function that filters CompEvents[] by: typeNo, eventDate, name
export function filterSchedule(
  compEvents: CompEvent[],
  filter: string,
  filterDate: IsoDate | '',
  eventType: EventType | 'ALL'
): CompEvent[] {
  return compEvents.filter((compEvent) => {
    const hasTypeNoMatch = compEvent.typeNo.toLowerCase().indexOf(filter.toLowerCase()) > -1
    const hasNameMatch = compEvent.name.toLowerCase().indexOf(filter.toLowerCase()) > -1
    const hasDateMatch =
      filterDate === '' || getDatePartFromIsoString(compEvent.eventDate) === filterDate

    const hasEventTypeMatch = eventType === 'ALL' || compEvent.typeNo.substring(0, 1) === eventType

    return (hasTypeNoMatch || hasNameMatch) && hasDateMatch && hasEventTypeMatch
  })
}

export function factoryCompetitionOnTheDay(): CompetitionOnTheDay {
  return {
    id: 0,
    name: '',
    date: '',
    location: '',
    organiser: '',
    logo: '',
    checkIn: false,
    events: {},
    domain: '',
    trialResults: {},
    options: factoryCompetitionOptions(),
    cachedDataSummary: {
      cachedAt: '',
      competitionId: 0,
      messageCount: 0
    }
  }
}

export function factoryCompetitionOptions(): CompetitionOptions {
  return {
    checkIn: {
      enabled: false
    }
  }
}

export function factoryIndividualEntry(): IndividualEntry {
  return {
    entryId: 0,
    athleteId: 0,
    URN: '',
    firstName: '',
    surName: '',
    aoCode: '',
    perf: '0.00',
    eventAgeGroup: '',
    ageGroup: '',
    bibNo: '',
    teamBibNo: '',
    clubName: '',
    gender: 'M',
    seeding: {
      heatNo: 0,
      laneNo: 0
    },
    pof10: undefined,
    present: null,
    collected: 0,
    checkedIn: 0,
    heatNoCheckedIn: 0,
    laneNoCheckedIn: 0,
    classification: 0
  }
}

export function factoryCompEvent(): CompEvent {
  return {
    egId: 0,
    name: '',
    eventNo: 0,
    typeNo: 'T0',
    eventDate: '',
    egOptions: factoryCompetitionScheduleOptions(),
    entries: {},
    results: {}
  }
}

function factoryCompetitionScheduleOptions(): CompetitionScheduleOptions {
  return {
    maxathletes: 0,
    maxInHeat: 0,
    seed: {
      laneCount: 0,
      type: 'H',
      seeded: false,
      doubleup: '',
      qualifyToEg: {
        id: 0,
        compId: 0,
        name: '',
        eventNo: 0,
        rules: {
          auto: 0,
          nonAuto: 0
        },
        isMultiEvent: false
      }
    },
    checkIn: {
      from: -1,
      to: -1,
      seedOnEntries: false
    }
  }
}

export function compEventHasHowManyEntries(ce: CompEvent): number {
  return ce.entries !== undefined ? Object.keys(ce.entries).length : 0
}

export function compEventHasHowManyResultHeats(ce: CompEvent): number {
  return ce.results !== undefined ? Object.keys(ce.results).length : 0
}

export function howManyTeams(compEvent: CompEvent): number {
  return compEvent.teams ? compEvent.teams.length : 0
}

export function getCompEventStartTime(compEvent: CompEvent): string {
  return getTimePartFromIsoString(compEvent.eventDate)
}

export function doesDomainMatch(messageDomain: string, acceptableDomainList: string[]): boolean {
  if (!messageDomain) {
    //  socket messages can have no domain so return false
    return false
  }
  messageDomain = messageDomain.toLowerCase()

  // const junk: any[] = []

  const resBool = acceptableDomainList.some((domain) => {
    if (domain === '') {
      return false
    }
    if (messageDomain === '*') {
      return true
    }

    domain = domain.toLowerCase()
    /// remove http:// and https://
    domain = domain.replace('http://', '')
    domain = domain.replace('https://', '')
    const res = messageDomain.indexOf(domain) > -1
    // junk.push({ domain, res, messageDomain })
    return res
  })

  // junk.push('resBool: ' + resBool)

  return resBool
}

export function doesDomainMatchOrig(
  messageDomain: string,
  acceptableDomainList: string[]
): boolean {
  return acceptableDomainList.some((domain) => {
    return domain.indexOf(messageDomain) > -1
  })
}

/**
 *
 * @param results
 */
export function sortRaceResultsByPosition(results: IndividualResult[]): IndividualResult[] {
  //  get all results withg position 0
  const resultsWithPosition0 = results.filter((result) => {
    return result.position === 0
  })

  //  get all results with position > 0
  const resultsWithPositionGT0 = results.filter((result) => {
    return result.position > 0
  })

  //  concat the two arrays and sort by position
  return resultsWithPositionGT0.concat(resultsWithPosition0)
}

/**
 *
 * @param result
 */
export function getPosition(result: IndividualResult): string {
  return getPositionString(result.position)
}

/**
 *
 * @param position
 */
export function getPositionString(position: number): string {
  if (position === 0) {
    return '-'
  }
  return position.toString()
}

/**
 *
 * @param scoreValue
 */
export function cleanUpScoreValueForDisplay(scoreValue: ScoreNeedsCleaning | number): string {
  if (typeof scoreValue === 'number') {
    return scoreValue.toString()
  }
  return scoreValue.replace('!#!', '')
}

/**
 *
 * @param scoreValue
 * @param isTrack
 */
export function getScoreToDisplay(
  scoreValue: number | string | undefined | null,
  isTrack: boolean
): string {
  if (scoreValue === undefined) {
    return '-'
  }
  if (scoreValue === null) {
    return '-'
  }
  if (typeof scoreValue === 'number' && scoreValue === 0) {
    return '-'
  }
  const sanitisedString = cleanUpScoreValueForDisplay(scoreValue.toString())

  if (!isNumeric(sanitisedString)) {
    return sanitisedString
  }

  if (!isTrack) {
    //   Field event just return the score
    return formatTrackScore(sanitisedString)
  }
  return turnSecondsIntoHumanReadableTime(parseFloat(sanitisedString))
}

/**
 * Formats a track score to have 2 decimal places, but we don't
 * know if it's m and cm or cm and hundredths of a
 * @param score
 */
export function formatTrackScore(score: string | number): string {
  if (typeof score === 'number') {
    score = score.toString()
  }
  if (score.indexOf('.') === -1) {
    return score + '.00'
  }
  const scoreParts = score.split('.')
  // pad the seconds part with 0 if less than 2 characters
  const metres = scoreParts[0]
  const cms = scoreParts[1].padEnd(2, '0')
  return metres + '.' + cms
}

/**
 *
 * @param seconds
 */
export function turnSecondsIntoHumanReadableTime(seconds: number): string {
  // If less than 60 seconds then return the seconds and 100th of a second
  // If more than 60 seconds and less than 600 secondsthen return the minutes, seconds and 100th of a second
  // If more than 600 seconds and less than 60mins then return the minutes and seconds
  // If more than 60mins then return the hours, minutes and seconds
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  const remainingSecondsString = remainingSeconds.toFixed(2)
  const remainingSecondsStringParts = remainingSecondsString.split('.')
  const secondsString = remainingSecondsStringParts[0]
  const hundredthsString = remainingSecondsStringParts[1]

  if (seconds < 60) {
    return secondsString + '.' + hundredthsString.padStart(2, '0')
  }

  if (seconds < 3600) {
    return minutes + ':' + secondsString.padStart(2, '0') + '.' + hundredthsString.padStart(2, '0')
  }
  //display as hours, minutes and seconds
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  return (
    hours +
    ':' +
    remainingMinutes.toString().padStart(2, '0') +
    ':' +
    secondsString.padStart(2, '0')
  )

  //return minutes + ':' + secondsString.padStart(2, '0')
}

/**
 *
 * @param individualEntry
 */
export function hasPo10(individualEntry: IndividualEntry): boolean {
  return individualEntry.pof10 !== undefined && individualEntry.pof10.pbPerf !== undefined
}

/**
 *
 * @param pof10
 * @param bestType
 */
export function hasPo10Value(pof10: Pof10 | null | undefined, bestType: 'SB' | 'PB'): boolean {
  if (!pof10) {
    return false
  }
  const value = bestType === 'PB' ? pof10.pbPerf : pof10.sbPerf

  if (!value) {
    return false
  }

  const valueString = value.toString()
  if (valueString === '0.00') {
    return false
  }
  if (valueString === '0') {
    return false
  }
  if (valueString === '0.0') {
    return false
  }
  if (valueString === '') {
    return false
  }
  if (valueString.replace(/0/g, '').replace('.', '').length === 0) {
    return false
  }

  return valueString.length > 0
}

/**
 *
 * @param pof10
 * @param bestType
 * @param isTrack
 */
export function getPo10Value(
  pof10: Pof10 | null | undefined,
  bestType: 'SB' | 'PB',
  isTrack: boolean
): string {
  if (!pof10) {
    return '-'
  }

  const value = hasPo10Value(pof10, bestType)
    ? (bestType === 'PB' ? pof10!.pbPerf! : pof10!.sbPerf!).toString()
    : '-'
  return getScoreToDisplay(value, isTrack)
}

/**
 *
 * @param pof10
 * @param bestType
 * @param isTrack
 */
export function formatPo10DateAchieved(
  pof10: Pof10 | null | undefined,
  bestType: 'SB' | 'PB',
  isTrack: boolean
): string {
  if (!pof10) {
    return ''
  }

  const achieved: DateIsoIsh | null = bestType === 'PB' ? pof10.pbAchieved : pof10.sbAchieved
  if (!achieved) {
    return ''
  }
  //  Convert the achieved date to an Iso date string
  const achievedIsoDateTime = fixDateIsoIsh(achieved)
  // return formatDateTime(achievedIsoDateTime, 'DATE')
  return formatDateWithSuffix(achievedIsoDateTime)
}

/**
 *
 * @param compEvent
 */
export function getEntriesGroupedByHeatNo(
  compEvent: CompEvent
): Record<HeatNoString, IndividualEntry[]> {
  const entries = simpleClone(compEvent.entries)
  const result: Record<HeatNoString, IndividualEntry[]> = {}

  for (const entryId in entries) {
    const entry = entries[entryId]
    const heatNo = entry.seeding.heatNo.toString()
    if (!result[heatNo]) {
      result[heatNo] = []
    }
    result[heatNo].push(entry)
  }

  function laneNoPredicate(indivEvent: IndividualEntry): number {
    return indivEvent.seeding ? indivEvent.seeding.laneNo : 0
  }

  //  sort the entries by laneNo
  for (const heatNo in result) {
    result[heatNo] = sortArray(laneNoPredicate, result[heatNo], 'ASC')
  }

  return result
}

/**
 *
 * @param competitionOptions
 * @param compEvent
 */
export function getEntriesGroupedByHeatNoNew(
  competitionOptions: CompetitionOptions,
  compEvent: CompEvent
): {
  entriesGroupedByHeatNo: Record<HeatNoString, IndividualEntry[]>
  toBeSeeded: IndividualEntry[]
  notEligible: IndividualEntry[]
} {
  const entries = simpleClone(compEvent.entries)
  const result: Record<HeatNoString, IndividualEntry[]> = {}
  const toBeSeeded: IndividualEntry[] = []
  const notEligible: IndividualEntry[] = []

  const isCompUsingCheckIn = competitionOptions.checkIn.enabled

  for (const entryId in entries) {
    const entry = entries[entryId]

    let heatNo: string

    if (isCompUsingCheckIn) {
      const hasUserCollectedBib = entry.collected === 1
      const hasUserCheckedInForEvent = entry.checkedIn === 1

      if (hasUserCollectedBib && hasUserCheckedInForEvent) {
        const heatNoCheckedIn: number = entry.heatNoCheckedIn
        const laneNo = entry.laneNoCheckedIn
        const hasBeenSeeded = heatNoCheckedIn > 0 && laneNo > 0

        if (!hasBeenSeeded) {
          toBeSeeded.push(entry)
        } else {
          heatNo = heatNoCheckedIn.toString()
          if (!result[heatNo]) {
            result[heatNo] = []
          }
          result[heatNo].push(entry)
        }
      } else {
        notEligible.push(entry)
      }
    } else {
      heatNo = entry.seeding.heatNo.toString()
      if (!result[heatNo]) {
        result[heatNo] = []
      }
      result[heatNo].push(entry)
    }
  }

  function laneNoPredicate(indivEvent: IndividualEntry): number {
    if (isCompUsingCheckIn) {
      return indivEvent.laneNoCheckedIn
    }
    return indivEvent.seeding ? indivEvent.seeding.laneNo : 0
  }

  //  sort the entries by laneNo
  for (const heatNo in result) {
    result[heatNo] = sortArray(laneNoPredicate, result[heatNo], 'ASC')
  }

  return {
    entriesGroupedByHeatNo: result,
    toBeSeeded: toBeSeeded,
    notEligible: notEligible
  }
}

//  Function that returns whether CompEvent is track or field
export function isTrackEvent(compEvent: CompEvent): boolean {
  // return if 1st character of typeNo is T
  return compEvent.typeNo[0] === 'T'
}

export function isTeamEvent(compEvent: CompEvent): boolean {
  return !!(compEvent.teams && compEvent.teams.length > 0)
}

/**
 *
 * @param compEvent
 * @param searchTerm
 */
export function filterEntries(compEvent: CompEvent, searchTerm: string = ''): CompEvent {
  const scheduledEvent = simpleClone(compEvent)

  console.log('ResultService.filterEntries searchTerm: ' + searchTerm)
  if (!scheduledEvent.entries) {
    return scheduledEvent
  }

  const entries = simpleClone(scheduledEvent.entries)

  if (searchTerm === '') {
    return scheduledEvent
  }

  //  Loop across the entries and filter by firstName, surName, clubName
  const filteredEntries: Record<string, IndividualEntry> = {}
  for (const entryId in entries) {
    const entry = entries[entryId]
    const hasFirstNameMatch = entry.firstName.toLowerCase().includes(searchTerm.toLowerCase())
    const hasSurNameMatch = entry.surName.toLowerCase().includes(searchTerm.toLowerCase())
    const hasClubNameMatch = entry.clubName.toLowerCase().includes(searchTerm.toLowerCase())

    if (hasFirstNameMatch || hasSurNameMatch || hasClubNameMatch) {
      filteredEntries[entryId] = entry
    }
  }
  scheduledEvent.entries = filteredEntries
  return scheduledEvent
}

/**
 *
 * @param compEvent
 * @param searchTerm
 */
export function filterResults(compEvent: CompEvent, searchTerm: string = ''): CompEvent {
  const scheduledEvent = simpleClone(compEvent)

  console.log('ResultService.filterResults searchTerm: ' + searchTerm)
  if (!scheduledEvent.results) {
    return scheduledEvent
  }

  if (searchTerm === '') {
    return scheduledEvent
  }

  const results = simpleClone(scheduledEvent.results)

  //  Loop across the entries and filter by firstName, surName, clubName
  const filteredResults: Record<HeatNoString, IndividualResult> = {}
  for (const heatNo in results) {
    const resultsInHeat = results[heatNo]
    const resultsInHeatFiltered = resultsInHeat.filter((result) => {
      const hasNameMatch = result.athlete.toLowerCase().includes(searchTerm.toLowerCase())
      const hasClubNameMatch = result.affilation.toLowerCase().includes(searchTerm.toLowerCase())

      console.log('ResultService.filterResults hasNameMatch: ' + hasNameMatch)
      console.log('ResultService.filterResults hasClubNameMatch: ' + hasClubNameMatch)

      return hasNameMatch || hasClubNameMatch
    })

    results[heatNo] = resultsInHeatFiltered
  }
  scheduledEvent.results = results
  return scheduledEvent
}

/**
 *
 * @param compEvent
 */
export function howManyHeats(compEvent: CompEvent): number {
  if (!compEvent.results) {
    return 0
  }
  return Object.keys(compEvent.results).length
}

/**
 *
 * @param team
 */
export function getTeamAthleteNames(team: Team): string[] {
  if (!team.athletes) {
    return []
  }
  return team.athletes.map((athlete) => {
    return athlete.firstName + ' ' + athlete.surName
  })
}

/**
 *
 * @param team
 */
export function getTeamAthleteNamesString(team: Team): string {
  return getTeamAthleteNames(team).join(', ')
}

/**
 *
 * @param compEvent
 * @param searchTerm
 */
export function filterTeams(compEvent: CompEvent, searchTerm: string = ''): CompEvent {
  const scheduledEvent = simpleClone(compEvent)

  if (!scheduledEvent.teams) {
    scheduledEvent.teams = []
    return scheduledEvent
  }

  const teams = simpleClone(scheduledEvent.teams)

  if (searchTerm === '') {
    return scheduledEvent
  }

  //  Loop across the teams and filter by teamName
  const filteredTeams: Team[] = teams.filter((team) => {
    const teamNameMatch = team.teamName.toLowerCase().startsWith(searchTerm.toLowerCase())
    const athleteNameMatch = getTeamAthleteNames(team)
      .join(', ')
      .toLowerCase()
      .includes(searchTerm.toLowerCase())
    return teamNameMatch || athleteNameMatch
  })

  scheduledEvent.teams = filteredTeams
  return scheduledEvent
}

/**
 *
 * @param comp
 */
export function getCompetitionDates(comp: CompetitionOnTheDay): IsoDate[] {
  const events = comp.events
  const eventDates = Object.keys(events).map((key) => {
    // return the date part of the eventDate
    return getDatePartFromIsoString(events[key].eventDate)
  })

  const uniqueDates = new Set(eventDates)
  return Array.from(uniqueDates)
}

/**
 *
 * @param comp
 */
export function isMultiDayCompetition(comp: CompetitionOnTheDay): boolean {
  const dates = getCompetitionDates(comp)
  return dates.length > 1
}

/**
 *
 * @param comp
 * @param egId
 */
export function getCompetitionOnTheDayCompEventFromEgId(
  comp: CompetitionOnTheDay,
  egId: number
): CompEvent | undefined {
  if (!comp.events) {
    return undefined
  }
  return comp.events[egId.toString()]
}

/**
 *
 * @param comp
 * @param eventNo
 */
export function doesCompetitionOnTheDayHaveResultsForEvent(
  comp: CompetitionOnTheDay,
  eventNo: number
): boolean {
  const event = comp.events[eventNo]
  return event.results !== undefined && Object.keys(event.results).length > 0
}

/**
 *
 * @param comp
 * @param eventNo
 */
export function getCompetitionOnTheDayResultsForEvent(
  comp: CompetitionOnTheDay,
  eventNo: number
): Record<HeatNoString, IndividualResult[]> {
  const event = comp.events[eventNo]
  return event.results !== undefined && Object.keys(event.results).length > 0 ? event.results : {}
}

/**
 *
 * @param followingEvents
 * @param shouldFollowEvent
 */
export function updateCurrentlyFollowing(
  followingEvents: FollowingEvents,
  shouldFollowEvent: ShouldFollowEvent
): FollowingEvents {
  const currentlyFollowing = simpleClone(followingEvents)
  const eventKey = shouldFollowEvent.compEvent.egId.toString()
  if (shouldFollowEvent.isFollowing) {
    currentlyFollowing[eventKey] = {
      id: shouldFollowEvent.compEvent.egId,
      name: shouldFollowEvent.compEvent.name
    }
  } else {
    delete currentlyFollowing[eventKey]
  }
  return currentlyFollowing
}

/**
 *
 * @param originalResults
 * @param newResults
 * @param athleteId
 */
export function athleteReorderDetails(
  originalResults: IndividualResult[],
  newResults: IndividualResult[],
  athleteId: number
): {
  hasChangedPosition: boolean
  hasScoreChanged: boolean
  oldResult: IndividualResult | undefined
  newResult: IndividualResult | undefined
  originalIndex: number
  newIndex: number
  originalPosition: number
  newPosition: number
  originalScore: string
  newScore: string
} {
  const oldResult = originalResults.find((result) => {
    return result.athleteId === athleteId
  })

  const newResult = newResults.find((result) => {
    return result.athleteId === athleteId
  })

  const originalScore = oldResult ? oldResult.score : ''
  const newScore = newResult ? newResult.score : ''

  const originalIndex = oldResult ? originalResults.indexOf(oldResult) : -1
  const newIndex = newResult ? newResults.indexOf(newResult) : -1

  const originalPosition = oldResult ? oldResult.position : -1
  const newPosition = newResult ? newResult.position : -1

  const hasChangedPosition = originalIndex !== newIndex
  const hasScoreChanged = oldResult?.score !== newResult?.score

  return {
    hasChangedPosition,
    hasScoreChanged,
    oldResult,
    newResult,
    originalIndex,
    newIndex,
    originalPosition,
    newPosition,
    originalScore,
    newScore
  }
}

/**
 *
 * @param followingEvents
 * @param eventGroupId
 */
export function isUserFollowingEvent(
  followingEvents: FollowingEvents,
  eventGroupId: EventGroupId | EventGroupIdString
): boolean {
  if (Object.keys(followingEvents).length === 0) {
    return true
  }
  return (
    followingEvents[typeof eventGroupId === 'string' ? eventGroupId : eventGroupId.toString()] !==
    undefined
  )
}

/**
 *
 * @param trials
 */
export function getHighestScoreFromTrials(trials: AthleteTrialMap): ScoreNeedsCleaning {
  // if (!trials) {
  //   return ''
  // }
  // const trialKeys = Object.keys(trials)
  // if (trialKeys.length === 0) {
  //   return ''
  // }
  // const trialValues = Object.values(trials)
  // const trialValuesNumeric = trialValues.map((trial) => {
  //   return parseFloat(trial.toString())
  // })
  // const maxTrialValue = Math.max(...trialValuesNumeric)
  // return maxTrialValue.toString()

  let highestScore = 0
  let highestScoreString: ScoreNeedsCleaning = ''
  for (const key in trials) {
    const trialNumber: TrialNo = key as TrialNo
    const trialValue = trials[trialNumber]
    const cleanTrialScoreString = cleanUpScoreValueForDisplay(trialValue ? trialValue : '')

    let trialScore = 0
    if (isNumeric(cleanTrialScoreString)) {
      trialScore = Number(cleanTrialScoreString)
    }

    if (trialScore > highestScore) {
      highestScore = trialScore
      highestScoreString = typeof trialValue === 'string' ? trialValue : trialValue!.toString()
    }
  }
  return highestScoreString
}

/*
Below are two types of trial map let's call the second one normal and the first one abnormal write a function that can determine whether the trial map is of type normal or abnormal

{
	"1.75:1": "O",
	"1.80:1": "O",
	"1.85:1": "O",
	"1.90:1": "O",
	"1.95:1": "O",
	"2.00:1": "X",
	"2.00:2": "O",
	"2.05:1": "O",
	"2.10:1": "O"
}


{
	"t1": "6.6",
	"t2": "6.23"
}
 */
export function isTrialMapAbnormal(trialMap: AthleteTrialMap): boolean {
  return Object.keys(trialMap)[0].indexOf(':') > -1
}

/*
Create a function that takes this input and outputs The strings to the left the semi colons as the key with any others found group the value added to

input
{
	"1.75:1": "O",
	"1.80:1": "x",
	"1.80:2": "x",
	"1.80:3": "O",
	"1.85:1": "O",
	"1.90:1": "O",
	"1.95:1": "O",
	"2.00:1": "X",
	"2.00:2": "O",
	"2.05:1": "O",
	"2.10:1": "O"
}

output
{
	"1.75": "O",
	"1.80": "xxO",
	"1.85": "O",
	"1.90": "O",
	"1.95": "O",
	"2.00": "X0",
	"2.05": "O",
	"2.10": "O"
}
 */
export function getTrialsAbnormal(trialMap: Record<string, any>): AthleteTrialMapAbnormal {
  const trialMapAbnormal = simpleClone(trialMap)
  const trialMapNormal: AthleteTrialMapAbnormal = {}

  for (const key in trialMapAbnormal) {
    const keyParts: string[] = key.split(':')
    const trialKey = keyParts[0]
    const trialValue = trialMapAbnormal[key]
    if (!trialMapNormal[trialKey]) {
      trialMapNormal[trialKey] = trialValue
    } else {
      trialMapNormal[trialKey] += trialValue
    }
  }
  return trialMapNormal
}

export function getEventTypesFromSchedule(compEvents: CompEvent[]): Partial<EventTypeMap> {
  return compEvents.reduce((acc: Partial<EventTypeMap>, compEvent) => {
    const eventType = compEvent.typeNo.substring(0, 1) as EventType
    if (!acc[eventType]) {
      acc[eventType] = EventTypeDictionary[eventType]
    }
    return acc
  }, {})
}

export function getScheduledEventCss(
  scheduledEvent: CompEvent,
  followingEvents: FollowingEvents
): string[] {
  const css = []

  const resultCount = compEventHasHowManyResultHeats(scheduledEvent)

  if (resultCount > 0) {
    css.push('results-schedule--has-results')
  }

  const isFollowing = isUserFollowingEvent(followingEvents, scheduledEvent.egId)

  if (isFollowing && Object.keys(followingEvents).length > 0) {
    css.push('results-schedule--is-following')
  }

  return css
}

/**
 *
 * @param postcode
 */
export function isValidPostcode(postcode: string): boolean {
  const postcodeRegex = /^[A-Z]{1,2}[0-9]{1,2} ?[0-9][A-Z]{2}$/
  return postcodeRegex.test(postcode)
}

/**
 *
 * @param liveFeedMessages
 * @param followingEvents
 */
export function filterLiveFeedShowOnlyFollowing(
  liveFeedMessages: LiveFeedMessageBase[],
  followingEvents: FollowingEvents
): LiveFeedMessageBase[] {
  const followingCount = Object.keys(followingEvents).length

  if (followingCount === 0) {
    return liveFeedMessages
  }

  return liveFeedMessages.filter((message) => {
    let eventGroupId = -1
    const messageType = message.type
    // const messageTypes: LiveFeedMessageType[] = ['FIELD_RESULT',  'TRACK_RESULT', 'MANUAL_ENTRY']
    // if (!messageTypes.includes(messageType)) {
    //   eventGroupId = (message.payload.eventGroup.id
    // }
    if (messageType === 'FIELD_RESULT') {
      eventGroupId = (message.payload as LiveFeedMessageFieldResult).eventGroup.id
    }
    if (messageType === 'TRACK_RESULT') {
      eventGroupId = (message.payload as LiveFeedMessageTrackResult).eventGroup.id
    }
    // || messageType === 'MANUAL_ENTRY'
    //   if (message.payload && message.payload.eventGroup && message.payload.eventGroup.id) {
    //     eventGroupId = message.payload.eventGroup.id
    //   }

    return !!followingEvents[eventGroupId]
  })
}

// export function sortEntriesByPerformance(
//   results: IndividualResult[],
//   isTrack: boolean
// ): IndividualResult[] {
//   return results.sort((a, b) => {
//     const aScore = a.score ? parseFloat(a.score) : 0
//     const bScore = b.score ? parseFloat(b.score) : 0
//     return isTrack ? aScore - bScore : bScore - aScore
//   })
// }

export type EntriesGoodZero = {
  good: IndividualEntry[]
  zero: IndividualEntry[]
}

/**
 *
 * @param entries
 * @param isTrack
 */
export function sortEntriesByEstimatedPerformance(
  entries: IndividualEntry[],
  isTrack: boolean
): IndividualEntry[] {
  // filter out any entries that don't have a perf or are 0
  const entriesZero: EntriesGoodZero = entries.reduce<EntriesGoodZero>(
    (accum, entry) => {
      // is perf a number
      if (typeof entry.perf === 'number') {
        accum.good.push(entry)
        return accum
      }

      // If string, can it be converted to a number
      if (typeof entry.perf === 'string') {
        const isNumber = !isNaN(Number(entry.perf))

        if (isNumber) {
          // is number greater than 0
          if (Number(entry.perf) > 0) {
            accum.good.push(entry)
          } else {
            accum.zero.push(entry)
          }
        } else {
          accum.zero.push(entry)
        }
        return accum
      }

      return accum
    },
    { good: [], zero: [] }
  )

  entriesZero.good.sort((a, b) => {
    const aScore = a.perf ? Number(a.perf) : 0
    const bScore = b.perf ? Number(b.perf) : 0

    return isTrack ? aScore - bScore : bScore - aScore
  })

  return entriesZero.good.concat(entriesZero.zero)

  // return entries.sort((a, b) => {
  //   const aScore = a.perf ? Number(a.perf) : 0
  //   const bScore = b.perf ? Number(b.perf) : 0
  //
  //   return isTrack ? aScore - bScore : bScore - aScore
  // })
}

/**
 *
 * @param individualEntry
 */
export function getBibNo(individualEntry: IndividualEntry | undefined): string {
  if (!individualEntry) {
    return 'NA'
  }

  if (individualEntry && individualEntry.teamBibNo && individualEntry.teamBibNo !== '') {
    return individualEntry.teamBibNo
  }

  return individualEntry?.bibNo || ''
}

/**
 *
 * @param apiCompetitionSchedules
 */
export function createQualifyToFromMap(
  apiCompetitionSchedules: ApiCompetitionSchedule[]
): QualifyLinks {
  const recordQualifyLinks: QualifyLinks = {}

  const apiCompetitionSchedulesInternal = simpleClone(apiCompetitionSchedules)

  // create a map of egId to event name
  const egIdToNameMap: Record<string, string> = {}
  apiCompetitionSchedulesInternal.forEach((schedule: ApiCompetitionSchedule) => {
    egIdToNameMap[schedule.egId.toString()] = schedule.typeNo
  })

  // loop across all schedules
  apiCompetitionSchedulesInternal.forEach((schedule: ApiCompetitionSchedule) => {
    // does it have qualify info

    if (schedule.egOptions && typeof schedule.egOptions === 'string') {
      schedule.egOptions = JSON.parse(schedule.egOptions as any as string)
    }

    const egOptions: ApiCompetitionScheduleOptions =
      schedule.egOptions as any as ApiCompetitionScheduleOptions

    if (
      egOptions &&
      egOptions.seed &&
      egOptions.seed.qualifyToEg &&
      egOptions.seed.qualifyToEg.id &&
      egOptions.seed.qualifyToEg.id > 0
    ) {
      const qualifyToEg = egOptions.seed.qualifyToEg
      const egId = schedule.egId.toString()
      const qualifyToEgId = qualifyToEg.id

      if (!recordQualifyLinks[egId]) {
        recordQualifyLinks[egId] = {
          from: null,
          to: null
        }
      }

      if (qualifyToEgId) {
        if (!recordQualifyLinks[qualifyToEgId]) {
          recordQualifyLinks[qualifyToEgId.toString()] = {
            from: null,
            to: null
          }
        }
        recordQualifyLinks[qualifyToEgId].from = {
          egId: egId,
          name: schedule.typeNo
        }
      }

      recordQualifyLinks[egId].to = qualifyToEgId
        ? {
            egId: qualifyToEgId.toString(),
            name: egIdToNameMap[qualifyToEgId.toString()]
          }
        : null
    }
  })

  return recordQualifyLinks
}
