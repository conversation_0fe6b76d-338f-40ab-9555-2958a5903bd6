<template>
  <!--  <div class="e4s-flex-column">-->
  <div class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center">
    <div class="e4s-flex-row e4s-justify-flex-row-vert-center e4s-flex-grow">
      <InputDebounce
        class="e4s-square--right e4s-flex-grow"
        :class="filterSearchTerm.length ? 'e4s-input-field--highlight' : ''"
        placeholder="Search..."
        :value="filterSearchTerm"
        @input="onSearchTermChanged"
      />
      <ButtonGenericV2
        class="e4s-button--auto"
        with-input="right"
        @click="onSearchTermClear"
        :disabled="filterSearchTerm.length === 0"
        slot="after"
        text="X"
      />
    </div>
    <div class="e4s-flex-row--end">
      <slot name="after-search" />
    </div>
  </div>

  <!--  </div>-->
  <!--  <hr class="dat-e4s-hr-only dat-e4s-hr&#45;&#45;light" />-->
  <div
    v-for="(indivdualResults, heatNumber) in state.heats"
    class="e4s-flex-column e4s-gap--standard-x"
    :key="heatNumber"
  >
    <div class="e4s-vertical-spacer--standard"></div>
    <div class="e4s-flex-column e4s-lite--header-race-heat">
      <div class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center">
        <div v-if="state.howManyHeats > 1" class="e4s-header--400">
          <span v-text="state.isTrack ? 'Race' : 'Heat'"></span> {{ heatNumber }} of
          {{ state.howManyHeats }}
        </div>

        <!--        <div v-if="getQualifyingEventEgId > 0" class="e4s-header&#45;&#45;500">-->

        <!--        </div>-->

        <div class="e4s-flex-row--end" v-if="state.isTrack">
          <div
            class="e4s-flex-column e4s-justify-flex-end e4s-header--500"
            v-text="getTrackWind(heatNumber)"
          ></div>
        </div>
      </div>

      <div class="e4s-flex-row">
        <div
          class="e4s-flex-row e4s-justify-flex-end e4s-gap--standard e4s-header--500 e4s-justify-flex-row-vert-center"
        >
          Pos
          <!--          <ButtonGenericV2-->
          <!--            v-if="getQualifyingEventEgId > 0"-->
          <!--            button-type="secondary"-->
          <!--            class="e4s-button&#45;&#45;slim e4s-button&#45;&#45;auto"-->
          <!--            text="View Qualifying Event"-->
          <!--            @click="gotoQualifyingEvent"-->
          <!--          />-->
        </div>
        <div class="e4s-flex-column e4s-justify-flex-end e4s-flex-row--end e4s-header--500">
          Result
        </div>
      </div>
    </div>
    <div class="e4s-vertical-spacer--standard"></div>
    <div class="e4s-repeatable-grid--bottom" />
    <div>
      <RaceResults
        :store-comp-results-state="storeCompResultsState"
        :individual-results="indivdualResults"
        :entries="scheduledEvent.entries"
        :is-track="state.isTrack"
        :is-team-event="isTeamEvent"
        :heat-no="heatNumber"
        :event-trial-map="eventTrialMap"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type {
  AthleteTrialMap,
  CompEvent,
  HeatNoString,
  IndividualEntry,
  StoreCompResultsState
} from '@/components/results/store-comp-results'
import { computed, type PropType, reactive, ref, watch } from 'vue'
import RaceResults from '@/components/results/race-results/RaceResults.vue'
import * as ResultsService from '@/components/results/results-service'
import InputDebounce from '@/common/ui/fields/input-debounce.vue'
import { simpleClone } from '@/services/common-service-utils'
import type { AthleteIdString, EventGroupId } from '@/services/common'
import ButtonGenericV2 from '@/common/ui/buttons/button-generic-v2.vue'

const props = defineProps({
  storeCompResultsState: {
    type: Object as PropType<StoreCompResultsState>,
    required: true
  },
  scheduledEvent: {
    type: Object as PropType<CompEvent>,
    required: true
  },
  eventTrialMap: {
    type: Object as PropType<Record<AthleteIdString, AthleteTrialMap>>,
    default: () => {
      return {}
    }
  },
  filterSearchTerm: {
    type: String,
    default: ''
  }
})

const emit = defineEmits<{
  (e: 'searchTermChanged', value: string): void
  (e: 'gotoQualifyingEvent', value: EventGroupId): void
}>()

watch(
  () => props.scheduledEvent,
  (newvalue, oldValue) => {
    console.log('HeatResults scheduledEvent changed', newvalue, oldValue)
    init()
  }
)

watch(
  () => props.filterSearchTerm,
  (newVal, oldValue) => {
    if (newVal !== filterTerm.value) {
      console.log('HeatResults.filterSearchTerm changed', newVal, oldValue)
      filterTerm.value = newVal
    }
  }
)

const filterTerm = ref('')

const state = reactive({
  searchTerm: '',
  howManyHeats: 0,
  heats: {},
  isTrack: false
})

init()

// function doFilter(searchTerm: string) {
//   console.log('HeatResults.doFilter')
//   state.searchTerm = searchTerm
//   init()
// }

function init() {
  let compEvent = simpleClone(props.scheduledEvent)
  state.howManyHeats = ResultsService.howManyHeats(compEvent)

  // if (state.searchTerm.length > 0) {
  //   console.log('HeatResults.init() searchTerm: ' + state.searchTerm + '...')
  //   compEvent = ResultsService.filterResults(compEvent, state.searchTerm)
  // }

  if (compEvent.results) {
    state.heats = compEvent.results
  }
  state.isTrack = ResultsService.isTrackEvent(compEvent)
}

function onSearchTermClear() {
  emit('searchTermChanged', '')
}

function onSearchTermChanged(searchTerm: string) {
  console.log('HeatResults.onSearchTermChanged', searchTerm)
  emit('searchTermChanged', searchTerm)
}

const isTeamEvent = computed(() => {
  return ResultsService.isTeamEvent(props.scheduledEvent)
})

// const getQualifyingEventEgId = computed<number>(() => {
//   if (!props.scheduledEvent.egOptions) {
//     return 0
//   }
//   if (!props.scheduledEvent.egOptions.seed) {
//     return 0
//   }
//
//   if (!props.scheduledEvent.egOptions.seed.qualifyToEg) {
//     return 0
//   }
//
//   if (!props.scheduledEvent.egOptions.seed.qualifyToEg.id) {
//     return 0
//   }
//
//   return props.scheduledEvent.egOptions.seed.qualifyToEg.id
// })

function getTrackWind(heatNo: HeatNoString) {
  // return (heatNo: HeatNoString) => {
  //   return state.getEntriesGroupedByHeatNo[heatNo][0].seeding.wind
  // }
  // return 'Wind: ' + props.compEvent.trackWind

  const heats: Record<HeatNoString, IndividualEntry[]> = state.heats

  // get heat by heatNo from state.getEntriesGroupedByHeatNo
  const heat = heats[heatNo.toString()]
  if (!heat) {
    return 'NA'
  }
  // get first entry
  const entry = heat[0]
  if (!entry) {
    return 'NA'
  }
  // get wind
  const wind = entry.wind
  if (!wind) {
    return 'NA'
  }

  console.log('HeatResults.getTrackWind', wind)

  return 'Wind: ' + wind.toString() + ' m/s'
}

function gotoQualifyingEvent() {
  console.log('GotoQualifyingEvent')
  // emit('gotoQualifyingEvent', getQualifyingEventEgId.value)
}
</script>

<style></style>
