import { computed, reactive, ref } from 'vue'
import type {
  AthleteIdString,
  DateIso,
  DateIsoIsh,
  DateTimeIso,
  DateTimeUTC,
  EntryIdString,
  EventGroupId,
  EventGroupIdString,
  EventId,
  EventIdString
} from '@/services/common'

import * as ResultsService from '@/components/results/results-service'
import * as CommonServiceUtils from '@/services/common-service-utils'
import { useEventListener, useVibrate, useWakeLock, useWebSocket } from '@vueuse/core'
import type {
  ResultSocketActionType,
  SocketListenConfig,
  SocketResultData
} from '@/components/results/socket/result-socket-models'
import type {
  AO_CODE,
  EventType,
  EventTypeMap,
  IBaseConcrete,
  IsoDate
} from '@/common/common-models'
import * as SocketService from '@/components/results/socket/socket-service'
import type {
  FieldResults_AthleteResult,
  SocketResultMessagePayloadFieldResults
} from '@/components/results/socket/socket-field-results/socket-field-results-models'
import * as SocketFieldResultsService from '@/components/results/socket/socket-field-results/socket-field-results-service'
import type { SocketResultMessagePayloadResultsManual } from '@/components/results/socket/socket-results-manual/socket-results-manual-models'
import * as SocketResultsManualService from '@/components/results/socket/socket-results-manual/socket-results-manual-service'
import type {
  LiveFeedMessageBase,
  LiveFeedMessageBaseGeneric
} from '@/components/results/socket/live-feed/live-feed-models'
import * as LiveFeedService from '@/components/results/socket/live-feed/live-feed-service'
import { io } from 'socket.io-client'

import type { SocketsResultsPfPayload } from '@/components/results/socket/socket-results-pf/sockets-results-pf-models'
import * as SocketsResultsPfService from '@/components/results/socket/socket-results-pf/sockets-results-pf-service'
import { convertExternalJsonToCompetitionOnTheDay } from '@/components/results/middleware/competition-converter.service'
import type {
  ApiCompetitionAthlete,
  ApiCompetitionCachedDataSummary,
  ApiCompetitionEntry,
  ApiCompetitionOnTheDay,
  ApiCompetitionOptions,
  ApiCompetitionSchedule,
  ApiCompetitionScheduleOptions,
  ApiServerResponse
} from '@/components/results/middleware/competition-converter.models'
import type { SocketResultMessagePayloadAthleteBibNo } from '@/components/results/socket/models/athlete-bibno'
import type { SocketResultMessagePayloadFieldNextup } from '@/components/results/socket/models/field-nextup'
import { RESULT_SOCKET_ACTION_TYPES } from '@/components/results/socket/result-socket-models'
// import { factoryLiveFeedMessageField } from '@/components/results/socket/live-feed/live-feed-service'
// import { createGuid } from '@/services/common-service-utils'
import type { LiveFeedSocketMessage } from '@/components/results/socket/live-feed/models/live-feed-models'
import type { SocketResultMessagePayloadSeedDropped } from '@/components/results/socket/seed-dropped/models/seed-dropped'
import { updateSeedDropped } from '@/components/results/socket/seed-dropped/models/seed-dropped-service'
import type { SocketResultMessagePayloadTrackMove } from '@/components/results/socket/track-move/models/track-move'
import type { SocketResultMessagePayloadTrackMoveVert } from '@/components/results/socket/track-movevert/models/track-movevert'
import { updateTrackMove } from '@/components/results/socket/track-move/models/track-move-service'
import { updateTrackMoveVert } from '@/components/results/socket/track-movevert/models/track-movevert-service'
import type { SocketResultMessagePayloadEntriesPresent } from '@/components/results/socket/entries-present/models/entries-present'
import { updateEntriesPresent } from '@/components/results/socket/entries-present/models/entries-present-service'
import type { SocketResultMessagePayloadSeedConfirmed } from '@/components/results/socket/seed-confirmed/models'
import { updateSeedConfirmed } from '@/components/results/socket/seed-confirmed/models/seed-confirmed-service'
import type { SocketResultMessagePayloadEntriesCheckin } from '@/components/results/socket/entries-checkin/models'
import { updateEntriesCheckIn } from '@/components/results/socket/entries-checkin/models/entries-checkin-service'
import { isUtcTime, simpleClone } from '@/services/common-service-utils'

// import { serverResponse697 } from '@/components/results/mocks/comps/comp697'
// import { mockLiveFeedF26Hammer } from '@/components/results/socket/live-feed/mock-live-feed-f26-hammer'
// import { mockFieldResultsCardHammerPayload } from '@/components/results/socket/socket-field-results/mock-hammer-field-results'
// import { mockF26HammerPayload } from '@/components/results/socket/socket-field-results/mockF26Hammer'
// import { mockPhotoFinishT1Payload } from '@/components/results/socket/socket-results-pf/mock/mockPhotoFinishT1'
// import { mockF26HammerMatthewPayload } from '@/components/results/socket/socket-field-results/mockF26HammerMatthew'
// import { mockPhotoFinishT1Payload } from './socket/socket-results-pf/mock/mockPhotoFinishT1'

export type EntryGender = 'M' | 'F'
export type TrackTypeNo = `T${number}`
export type FieldTypeNo = `F${number}`
// export type ResultPositionString = string
export type HeatNoString = string
export type ScoreNeedsCleaning = string
export type TrialNo = 't1' | 't2' | 't3' | 't4' | 't5' | 't6' | 't7' | 't8'
export type TrialMap = Record<TrialNo, ScoreNeedsCleaning | number>
export type AthleteTrialMap = Partial<TrialMap>

// In athletics when they're doing heights or long jumps the symbols they used to indicate success pass failure
export type AthleteTrialAbnormalType = 'x' | 'X' | 'o' | 'O' | '-'

export type CompetitionOptions = ApiCompetitionOptions

/*
below might be
{
  '1.75': 'O',
  '1.80': 'xxO',  <---
  '1.85': 'O',
  '1.90': 'O',
  '1.95': 'O',
  '2.00': 'X0',
  '2.05': 'O',
  '2.10': 'O'
}
 */
export type AthleteTrialMapAbnormal = Record<string, string>

export interface CompetitionOnTheDayServer {
  compData: CompetitionOnTheDay
  liveFeed: LiveFeedMessageServer[]
  socketId: number // When comp periodically saves the socketId indicates which messages have been processed
}

export type SocketPayloadAccepted =
  | SocketResultMessagePayloadResultsManual
  | SocketResultMessagePayloadFieldResults
  | SocketsResultsPfPayload
  | SocketResultMessagePayloadAthleteBibNo
  | SocketResultMessagePayloadFieldNextup

export interface LiveFeedMessageServer {
  id: number
  created: DateTimeIso
  action: ResultSocketActionType
  payload:
    | string
    | SocketResultMessagePayloadResultsManual
    | SocketResultMessagePayloadFieldResults
    | SocketsResultsPfPayload
}

export type CompTrialResults = Record<
  EventIdString | EventId,
  Record<AthleteIdString, AthleteTrialMap>
>

export type CompetitionCachedDataSummary = ApiCompetitionCachedDataSummary

export interface CompetitionOnTheDay {
  id: number
  name: string
  date: DateIso
  location: string
  organiser: string
  checkIn: boolean
  logo: string
  events: Record<EventGroupIdString, CompEvent>
  domain: string
  trialResults: CompTrialResults
  options: CompetitionOptions
  cachedDataSummary: CompetitionCachedDataSummary
}

export interface CompEventBase {
  egId: EventGroupId
  name: string
}

export type CompetitionScheduleOptions = ApiCompetitionScheduleOptions

export interface CompEvent extends CompEventBase {
  eventNo: number
  eventText?: string // E.g. an age group
  typeNo: TrackTypeNo | FieldTypeNo
  eventDate: DateTimeUTC
  egOptions: CompetitionScheduleOptions
  entries?: Record<AthleteIdString, IndividualEntry>
  results?: Record<HeatNoString, IndividualResult[]>
  teams?: Team[]
}

export interface ApiScheduleResponse {
  events: Record<EventGroupIdString, CompEvent>
}

export interface Pof10 {
  pbPerf: null | number | string
  pbAchieved: null | DateIsoIsh
  sbPerf: null | number | string
  sbAchieved: null | DateIsoIsh
}

export interface Seeding {
  heatNo: number
  laneNo: number
}

export interface IndividualEntry {
  entryId: number
  athleteId: number
  URN: string
  firstName: string
  surName: string
  aoCode: AO_CODE
  perf: string | number | null | undefined // '0.00'
  eventAgeGroup: string // 'Under 15'
  ageGroup: string // 'Under 15'
  bibNo: string | null
  teamBibNo: string
  clubName: string
  gender: EntryGender
  seeding: Seeding
  pof10?: Pof10
  present: 1 | 0 | null
  wind?: string | number
  collected: 1 | 0 // Collected their bib
  checkedIn: 1 | 0 // Checked in for that event
  heatNoCheckedIn: number
  laneNoCheckedIn: number
  classification: number
}

export interface IndividualResult {
  laneNo: number
  position: number
  wind: string
  athlete: string // 'John Smith'
  athleteId: number //  Might not match an entry...as they were a reserve
  affilation: string // 'Unattached',
  bibNo: string //  '1',
  score: string //  '9.80',
  qualify: string | null //  '' ?
  animateThisAthlete?: boolean
  fieldResultAthlete?: FieldResults_AthleteResult
}

export interface StoreCompResultsState {
  isReady: boolean
  isLoading: boolean
  loadingMessage: string
  error: string | null
  apiServerResponse: ApiServerResponse | null
  competitionOnTheDay: CompetitionOnTheDay
  longPollDateTime: DateTimeIso
  liveFeedLoad: {
    receivedOnLoad: LiveFeedMessageServer[]
    processed: LiveFeedMessageServer[]
  }
  dates: DateIso[]
  scheduledEvents: CompEvent[]
  schedule: {
    sortBy: keyof CompEvent
    filterTerm: string
    filterDateNextSequence: IsoDate //  The next date in the sequence, will keep this constant.
    filterDate: IsoDate
    filterEventType: EventType | 'ALL'
    filterIsEnabled: {
      any: boolean
      date: boolean
      eventType: boolean
      term: boolean
      count: number
    }
    eventTypesAvailable: Partial<EventTypeMap>
  }
  selectedEvent: {
    compEvent: CompEvent
    compEventFiltered: CompEvent //  For filtering entries, a copy of the original "compEvent"
    searchTerm: string //  For filtering entries
    eventTimeDisplay: string
    eventAgeGroup: string
    hasEntries: boolean
    hasResults: boolean
    isTrack: boolean
    isTeam: boolean
    qualify: {
      forward: {
        egId: number
        name: string
      }
      backward: {
        egId: number
        name: string
      }
    }
  }
  entries: IndividualEntry[]
  maps: {
    eventsByEventGroupId: Record<EventGroupIdString, ApiCompetitionSchedule[]>
    entriesByEntryId: Record<EntryIdString, EventGroupIdString>
    socketsByKey: Record<string, SocketResultData<unknown>>
    qualifyLinks: QualifyLinks
  }
  athletes: Record<AthleteIdString, ApiCompetitionAthlete>
  ui: {
    showSection: 'SCHEDULE' | 'EVENT_DETAIL' | 'LIVE_FEED'
    eventDetail: {
      showTab: 'DETAIL_ENTRIES' | 'DETAIL_START_LIST' | 'DETAIL_RESULTS'
    }
    showFollowEventSetup: boolean
    showingFilters: boolean
  }
  text: {
    date: string
  }
  socket: {
    isConnected: boolean
    deviceKey: string
    compAtSocketId: number
    connectedAt: Date | null
    disconnectedAt: Date | null
    messagesToProcess: unknown[]
    liveFeed: LiveFeedMessageBase[]
    liveFeedSocketMessages: LiveFeedSocketMessage[]
    liveFeedQueueSize: number
    followingEvents: FollowingEvents
  }
}

export interface Team {
  athletes: TeamAthlete[]
  id: number
  teamName: string
  teamBibNo: string | number | null
  egId: number
  seeding?: Seeding | {}
}

export interface TeamAthlete {
  id: number
  firstName: string
  surName: string
}

export type FollowingEvents = Record<EventIdString, IBaseConcrete>

export interface ShouldFollowEvent {
  isFollowing: boolean
  compEvent: CompEventBase
}

export type QualifyLink = {
  from: {
    egId: EventGroupIdString
    name: string
  } | null
  to: {
    egId: EventGroupIdString
    name: string
  } | null
}

export type QualifyLinks = Record<EventGroupIdString, QualifyLink>

const globalState = reactive<StoreCompResultsState>({
  apiServerResponse: null,
  competitionOnTheDay: ResultsService.factoryCompetitionOnTheDay(),
  longPollDateTime: '',
  liveFeedLoad: {
    receivedOnLoad: [],
    processed: []
  },
  dates: [],
  isReady: false,
  isLoading: false,
  loadingMessage: '',
  error: null,
  schedule: {
    sortBy: 'eventDate',
    filterTerm: '',
    filterDateNextSequence: '',
    filterDate: '',
    filterEventType: 'ALL',
    filterIsEnabled: {
      any: false,
      date: false,
      eventType: false,
      term: false,
      count: 0
    },
    eventTypesAvailable: {}
  },
  scheduledEvents: [],
  selectedEvent: {
    compEvent: ResultsService.factoryCompEvent(),
    compEventFiltered: ResultsService.factoryCompEvent(),
    searchTerm: '',
    eventTimeDisplay: '',
    eventAgeGroup: '',
    hasEntries: false,
    hasResults: false,
    isTrack: false,
    isTeam: false,
    qualify: {
      forward: {
        egId: 0,
        name: ''
      },
      backward: {
        egId: 0,
        name: ''
      }
    }
  },
  entries: [],
  athletes: {},
  maps: {
    eventsByEventGroupId: {},
    entriesByEntryId: {},
    socketsByKey: {},
    qualifyLinks: {}
  },
  ui: {
    showSection: 'SCHEDULE',
    eventDetail: {
      showTab: 'DETAIL_ENTRIES'
    },
    showFollowEventSetup: false,
    showingFilters: false
  },
  text: {
    date: ''
  },
  socket: {
    isConnected: false,
    // Generate a unique device key
    deviceKey: 'client-' + Math.random(),
    compAtSocketId: 0,
    connectedAt: null,
    disconnectedAt: null,
    messagesToProcess: [],
    liveFeed: [],
    liveFeedSocketMessages: [],
    liveFeedQueueSize: 20,
    followingEvents: {}
  }
})

export function useStoreCompResults() {
  const state = globalState

  const debugMessages = ref<string[]>([])

  const SOCKET_URL_AWS = import.meta.env.VITE_APP_SOCKET_URL
  const SOCKET_KEEP_ALIVE_PAYLOAD_DOMAIN = 'entry4sports.co.uk'
  const SOCKET_DOMAINS = [
    'dev.entry4sports.co.uk',
    'uat.entry4sports.co.uk',
    'https://entry4sports.co.uk',
    'https://entry4sports.co.uk',
    'https://lite.entry4sports.co.uk/'
  ]

  /**
   * heartbeat: {} is not accepted by the server.
   */
  const socket = io(SOCKET_URL_AWS, {
    reconnectionAttempts: 10,
    reconnectionDelay: 2000,
    autoConnect: true
  })

  // Set up event handlers
  socket.on('connect', () => {
    console.log('Socket: connected', socket)
    state.socket.connectedAt = new Date()
    state.socket.isConnected = true
  })

  socket.on('disconnect', () => {
    console.log('Socket: disconnected')
    state.socket.disconnectedAt = new Date()
    state.socket.isConnected = false
  })

  socket.on('connect_error', () => {
    console.error('Socket: Failed to connect Socket.IO at: ' + new Date())
  })

  // Listen for subscription confirmations
  socket.on('subscribed', (data) => {
    console.log(`Subscribed to competition: ${data.comp.id}`)
  })

  socket.on('unsubscribed', (data) => {
    console.log(`Unsubscribed from competition: ${data.comp.id}`)
  })

  socket.on('message', (data) => {
    console.log('Socket: message received', data)
    // processSocketMessage(data)
  })

  //competition-update
  socket.on('competition-update', (data) => {
    console.log('Socket: competition-update received', data)
    processSocketMessage(data)
  })

  /**
   * use this sparingly.  It's a big payload.
   */
  socket.on('cache-updated', (data: { payload: ApiCompetitionOnTheDay }) => {
    console.log('Socket: competition-update received', data)
    initSetupCompetition(data.payload)
  })

  // Add after the other socket.on handlers
  socket.onAny((event, ...args) => {
    console.log(`Socket.onAny: caught event "${event}"`, args)
    // You can add additional monitoring logic here
  })

  //  Wakelock can NOT be activated automatically, it must be activated by a user gesture.
  const wakeLock = reactive(useWakeLock())
  //wakeLock.request('screen')
  const wakeLockDebug = ref('')

  const isWakeLockOnText = computed(() => (wakeLock.isActive ? 'ON' : 'OFF'))

  function wakeLockCToggle() {
    return wakeLock.isActive ? wakeLock.release() : wakeLock.request('screen')
  }

  //  Vibrate on socket message but can't get it to work on PDA, think blocked.
  const vibrateController = useVibrate({ pattern: [300, 100, 300] })

  /**
   * Listen for visibility change, if the page is visible then get the competition data.  E.g. user
   * has switched tabs and come back.
   */
  useEventListener(document, 'visibilitychange', (evt) => {
    console.log('visibilitychange', evt)

    const isVisible = evt.target && (evt.target as HTMLDocument).visibilityState === 'visible'

    if (isVisible) {
      console.log('visibilitychange = visible')
      // debugMessages.value.push('visibilitychange = visible at: ' + new Date())
      console.log('visibilitychange = visible at: ' + new Date(), simpleClone(state))
      getCompetitionOnTheDay(state.competitionOnTheDay.id, false)
    } else {
      console.log('visibilitychange = hidden at: ' + new Date())
    }
  })

  /**
   * Listen for clicks on the document, if the user clicks then check if the wakelock is active.
   * This bypasses the block for auto activate the wakelock.
   */
  useEventListener(document, 'click', (evt) => {
    console.log('document click', evt)
    checkWakeLock()
  })

  function init(compId: number) {
    reset()
    state.isLoading = true
    getCompetitionOnTheDay(compId, true).finally(() => {
      state.isLoading = false
    })
    joinSocketRoom(compId)
  }

  let pointerForSocketKeepAlive: number | null = null
  let pointerForCompetitionRefresh: number | null = null

  /**
   * Join the socket room for the competition.
   * @param compId
   */
  function joinSocketRoom(compId: number) {
    const message = {
      key: 0,
      comp: { id: compId },
      action: 'subscribe',
      deviceKey: state.socket.deviceKey,
      securityKey: '',
      domain: window.location.hostname,
      payload: { compId: compId }
    }

    socket.emit('message', JSON.stringify(message))
  }

  /**
   *
   */
  function reset() {
    state.competitionOnTheDay = ResultsService.factoryCompetitionOnTheDay()
    state.isLoading = false
    state.error = null

    state.scheduledEvents = []
    state.entries = []
  }

  /**
   *
   * @param id
   * @param initiateTimeout
   */
  function getCompetitionOnTheDay(id: number, initiateTimeout: boolean): Promise<void> {
    const url = import.meta.env.VITE_APP_API_HOST + '/api/competitions/' + id + '/on-the-day'

    // state.isLoading = true
    state.loadingMessage = 'Getting competition data...'

    return fetch(url)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`)
        }
        return response.json()
      })
      .then((data: ApiServerResponse) => {
        console.log('competitionOnTheDay response', data)

        initSetupCompetition(data.data)

        state.isLoading = false

        //
        if (initiateTimeout && data.data.competitionClientConfig.refreshIntervalMs) {
          const refreshIntervalMs = data.data.competitionClientConfig.refreshIntervalMs
          console.log('getCompetitionOnTheDay refreshIntervalMs: ' + refreshIntervalMs)
          scheduleCompetitionRefresh(id, refreshIntervalMs)
        }
      })
      .catch((error) => {
        console.error('Error fetching competition data:', error)
        state.error = error
        // state.isLoading = false
      })
  }

  /**
   *
   * @param id
   * @param refreshIntervalMs
   */
  function scheduleCompetitionRefresh(id: number, refreshIntervalMs: number) {
    console.log('scheduleCompetitionRefresh()...' + new Date())
    // Always clear any existing timeout first
    if (pointerForCompetitionRefresh !== null) {
      console.log('scheduleCompetitionRefresh() Clearing existing competition refresh timeout')
      window.clearTimeout(pointerForCompetitionRefresh)
      pointerForCompetitionRefresh = null
    }

    if (state.isLoading) {
      console.log('scheduleCompetitionRefresh()...competition is loading, exiting')
      return
    }

    const MINIMUM_REFRESH_INTERVAL_MS = 5000
    if (refreshIntervalMs < MINIMUM_REFRESH_INTERVAL_MS) {
      refreshIntervalMs = MINIMUM_REFRESH_INTERVAL_MS
    }

    // Set the new timeout
    pointerForCompetitionRefresh = window.setTimeout(() => {
      getCompetitionOnTheDay(id, true)
      // Set to null after execution to indicate no active timeout
      // pointerForCompetitionRefresh = null
    }, refreshIntervalMs)
  }

  /**
   *
   * @param apiCompetitionOnTheDay
   */
  function initSetupCompetition(apiCompetitionOnTheDay: ApiCompetitionOnTheDay) {
    console.log('init()')
    state.apiServerResponse = { data: apiCompetitionOnTheDay }
    state.longPollDateTime = new Date().toISOString()

    // loop accross apiServerResponse.data.athletes and add to state.athletes
    apiCompetitionOnTheDay.athletes.forEach((athlete: ApiCompetitionAthlete) => {
      state.athletes[athlete.id] = athlete
    })

    // Loop across the schedule setting maps events by Event Group ID
    // This is used as a quick look up object
    apiCompetitionOnTheDay.schedules.forEach((schedule: ApiCompetitionSchedule) => {
      if (!state.maps.eventsByEventGroupId[schedule.egId]) {
        state.maps.eventsByEventGroupId[schedule.egId] = []
      }
      state.maps.eventsByEventGroupId[schedule.egId].push(schedule)
    })

    state.maps.qualifyLinks = ResultsService.createQualifyToFromMap(
      apiCompetitionOnTheDay.schedules
    )

    // Map entries by entry id to event group id.
    // This is used as a quick look up object
    apiCompetitionOnTheDay.entries.forEach((entry: ApiCompetitionEntry) => {
      state.maps.entriesByEntryId[entry.entryId.toString()] = entry.egId
        ? entry.egId.toString()
        : ''
    })

    const competitionOnTheDay: CompetitionOnTheDay =
      convertExternalJsonToCompetitionOnTheDay(apiCompetitionOnTheDay)
    const competitionOnTheDayServer: CompetitionOnTheDayServer = {
      compData: competitionOnTheDay,
      liveFeed: apiCompetitionOnTheDay.socketMessages,
      socketId: 0
    }

    setUpCompetionOnTheDay(competitionOnTheDayServer)
  }

  /**
   *
   * @param competitionOnTheDayServer
   */
  function setUpCompetionOnTheDay(competitionOnTheDayServer: CompetitionOnTheDayServer) {
    state.loadingMessage = 'Setting up competition...'

    state.competitionOnTheDay = competitionOnTheDayServer.compData
    state.liveFeedLoad.receivedOnLoad = competitionOnTheDayServer.liveFeed
    state.text.date = CommonServiceUtils.getFormattedDate(state.competitionOnTheDay.date)
    state.dates = CommonServiceUtils.unique(
      ResultsService.getCompetitionDates(competitionOnTheDayServer.compData)
    )

    state.schedule.filterDateNextSequence = CommonServiceUtils.getNextDateInSequence(state.dates)
    state.schedule.filterDate = state.schedule.filterDateNextSequence

    const eventTypesForSelectFilter = {
      ALL: 'All',
      ...ResultsService.getEventTypesFromSchedule(Object.values(state.competitionOnTheDay.events))
    }

    // console.warn('eventTypesForSelectFilter', eventTypesForSelectFilter)

    state.schedule.eventTypesAvailable = eventTypesForSelectFilter

    state.socket.compAtSocketId = competitionOnTheDayServer.socketId

    state.loadingMessage = 'Loading live feeds...'
    setUpLiveFeed()
    state.isReady = true

    setScheduleToDisplay()

    // if an event is currently selected, ensure latest results are shown
    if (state.selectedEvent.compEvent.egId > 0) {
      const latestCompEventFromServer =
        state.competitionOnTheDay.events[state.selectedEvent.compEvent.egId.toString()]

      processUpdateSelectedEvent(latestCompEventFromServer)
    }
  }

  /**
   * Check if the wakelock is active, if not then request it.
   * Debug there in attempt to get it to set automatically.
   */
  function checkWakeLock() {
    wakeLockDebug.value = 'A...'

    if (!wakeLock.isActive) {
      wakeLockDebug.value = 'A...release'
      wakeLock
        .request('screen')
        .then(() => {
          wakeLockDebug.value = 'B...then'
        })
        .catch((err) => {
          wakeLockDebug.value = 'B...catch' + err
        })
        .finally(() => {
          // wakeLockDebug.value = 'B...finally'
        })
    } else {
      wakeLockDebug.value = 'A...already active'
    }
  }

  function setEventTypeFilter(eventType: EventType | 'ALL') {
    state.schedule.filterEventType = eventType
    setScheduleToDisplay()
  }

  function setShowFilters(showingFilters: boolean) {
    state.ui.showingFilters = showingFilters
  }

  function filterScheduleDate(filterDate: IsoDate) {
    console.log('filterScheduleDate() filterDate: ' + filterDate)
    state.schedule.filterDate = filterDate
    setScheduleToDisplay()
  }

  function filterSchedule(filterTerm: string) {
    state.schedule.filterTerm = filterTerm
    setScheduleToDisplay()
  }

  function clearFilterSchedule() {
    state.schedule.filterTerm = ''
    setScheduleToDisplay()
  }

  /**
   *
   */
  function setScheduleToDisplay() {
    //  pass thru any filters.
    console.log('setScheduleToDisplay()>>>>>>>>>>>>>>>>>>>>>>>>>>>>')
    const schedule = ResultsService.sortSchedule(
      CommonServiceUtils.simpleClone(Object.values(state.competitionOnTheDay.events)),
      state.schedule.sortBy
    )

    //  Are there any filters enabled?
    const isDateFilter = state.schedule.filterDate !== state.schedule.filterDateNextSequence
    const isEventTypeFilter = state.schedule.filterEventType !== 'ALL'
    const isTermFilter = state.schedule.filterTerm !== ''

    state.schedule.filterIsEnabled.date = isDateFilter
    state.schedule.filterIsEnabled.eventType = isEventTypeFilter
    state.schedule.filterIsEnabled.term = isTermFilter

    state.schedule.filterIsEnabled.any = isDateFilter || isEventTypeFilter || isTermFilter

    state.schedule.filterIsEnabled.count = [isDateFilter, isEventTypeFilter, isTermFilter].filter(
      (f) => f
    ).length

    state.scheduledEvents = ResultsService.filterSchedule(
      schedule,
      state.schedule.filterTerm,
      state.schedule.filterDate,
      state.schedule.filterEventType
    )
  }

  /**
   *
   */
  function setUpLiveFeed() {
    //  loop through all messages in state.liveFeedLoad.receivedOnLoad check if payload is string,
    //  see what action it is, secheck by id that it is not in the state.liveFeedLoad.processed array
    // if not then process it and add it to the state.liveFeedLoad.processed array
    //  then add it to the liveFeed array
    // sort liveFeedMessages by id, so oldest first (with lowest id)
    const liveFeedMessages = CommonServiceUtils.simpleClone(state.liveFeedLoad.receivedOnLoad).sort(
      (a, b) => {
        return a.id - b.id
      }
    )
    const processedMessages = CommonServiceUtils.simpleClone(state.liveFeedLoad.processed)

    console.log(
      'setUpLiveFeed() liveFeedMessages, process only above compAtSocketId: ' +
        state.socket.compAtSocketId
    )

    // const liveFeed = simpleClone(state.socket.liveFeed)
    for (const message of liveFeedMessages) {
      let payload:
        | SocketResultMessagePayloadResultsManual
        | SocketResultMessagePayloadFieldResults
        | SocketsResultsPfPayload =
        typeof message.payload === 'string' ? JSON.parse(message.payload) : message.payload
      if (!payload) {
        continue
      }

      const messageId = message.id
      // const isGreaterThanCompAtSocketId = messageId > state.socket.compAtSocketId
      // if (!isGreaterThanCompAtSocketId) {
      //   console.log(
      //     'setUpLiveFeed() message already processed by server: ' +
      //       messageId +
      //       ' is less than ' +
      //       state.socket.compAtSocketId
      //   )
      //   continue
      // }
      const found = processedMessages.find((processedMessage) => {
        return processedMessage.id === messageId
      })
      if (!found) {
        processedMessages.push(message)

        const isLoadingLiveFeed = true
        // TODO
        // processSocketPayload(message as any as SocketResultData<unknown>, isLoadingLiveFeed)
        processSocketPayload(message as any as SocketResultData<unknown>)
      }
    }
  }

  function testSocketMessage(whichOne: 'finley' | 'isabelle' | 'matthew' | 'matthew2' | 'pf1') {
    // const dataPayload = whichOne === '1' ? mockFieldResultsCardHammerPayload : mockF26HammerPayload
    /*
    const dataMap = {
      finley: mockLiveFeedF26Hammer,
      isabelle: mockFieldResultsCardHammerPayload,
      matthew: mockF26HammerPayload,
      matthew2: mockF26HammerMatthewPayload,
      pf1: mockPhotoFinishT1Payload
    }

    const dataPayload = dataMap[whichOne]

    if (whichOne === 'pf1') {
      socketProcessPhotoResults(dataPayload as SocketsResultsPfPayload)
    } else {
      socketProcessFieldResults(dataPayload as SocketResultMessagePayloadFieldResults)
    }

    const liveFeedMessageBase = LiveFeedService.factoryLiveFeedMessageField(
      whichOne === 'pf1' ? 'photofinish' : 'field-results',
      dataPayload
    )
    if (liveFeedMessageBase) {
      addToLiveFeed(liveFeedMessageBase)
    }
    */
  }

  /**
   *
   * @param messageEvent
   */
  function processSocketMessage(messageEvent: MessageEvent) {
    let messageData
    // if ((messageEvent as MessageEvent).data) {
    messageData = (messageEvent as MessageEvent).data || messageEvent
    // } else {
    //   messageData = (messageEvent as [string, unknown])[1]
    // }

    // const messageData  = messageEvent.data ? messageEvent.data : messageEvent
    const messageDataType = typeof messageData

    console.log('processSocketMessage messageDataType: ' + messageDataType)

    const socketMessage: SocketResultData<unknown> = messageData

    console.log('processSocketMessage socketMessage', socketMessage)

    // This is not really relevant anymore since socket listens to
    // messages for a specific comp.
    const socketListenConfig: SocketListenConfig = {
      listenForCompIds: [state.competitionOnTheDay.id],
      listenForMessageDomains: SOCKET_DOMAINS,
      listenForMessageTypes: [...RESULT_SOCKET_ACTION_TYPES]
    }

    const shouldAccept = SocketService.shouldSocketMessageBeAccepted(
      socketMessage,
      socketListenConfig
    )

    console.log('processSocketMessage shouldAccept', shouldAccept)

    if (shouldAccept.should) {
      console.log('shouldAccept', shouldAccept)

      vibrateController.vibrate([300, 100, 300])

      // processSocketPayload(socketMessage.action, socketMessage.payload as SocketPayloadAccepted)
      processSocketPayload(socketMessage)
    } else {
      console.warn(
        'processSocketMessage() shouldAccept.should: ' +
          shouldAccept.should +
          ' - ' +
          shouldAccept.reason
      )
    }
  }

  /**
   *
   * @param socketResultData
   */
  function processSocketPayload(
    socketResultData: SocketResultData<unknown>
    // action: ResultSocketActionType,
    // payload: SocketPayloadAccepted,
    // liveFeedMessageServer: LiveFeedMessageServer | undefined = undefined
  ) {
    let liveFeedMessageBase: LiveFeedMessageBase | LiveFeedMessageBaseGeneric<unknown> | undefined =
      undefined

    // let eventNo: EventId | EventIdString = 0
    let isUserFollowingEvent = true
    const action = socketResultData.action
    const payload = socketResultData.payload

    // const messageId = liveFeedMessageServer ? liveFeedMessageServer.id : -1
    const messageId = -1

    const isGreaterThanCompAtSocketId =
      messageId === -1 ? true : messageId > state.socket.compAtSocketId
    console.log(
      'setUpLiveFeed() messageId: ' +
        messageId +
        ', server: ' +
        state.socket.compAtSocketId +
        ', isGreaterThanCompAtSocketId: ' +
        isGreaterThanCompAtSocketId +
        (isGreaterThanCompAtSocketId ? ' - OK' : ' - NO need to process')
    )

    if (action === 'cache-updated') {
      debugger
      const payloadCacheUpdated = payload as CompetitionOnTheDay
      console.log('processSocketPayload() cache-updated', payloadCacheUpdated)
      // socketProcessCacheUpdated(payloadCacheUpdated)
    }

    if (action === 'athlete-bibno') {
      const payloadAthleteBibNo = payload as SocketResultMessagePayloadAthleteBibNo
      console.log('processSocketPayload() athlete-bibno', payloadAthleteBibNo)
      if (isGreaterThanCompAtSocketId) {
        socketProcessAthleteBibNo(payloadAthleteBibNo)
      }
    }

    if (action === 'field-nextup') {
      const payloadFieldNextup = payload as SocketResultMessagePayloadFieldNextup
      console.log('processSocketPayload() field-nextup', payloadFieldNextup)
      if (isGreaterThanCompAtSocketId) {
        socketProcessFieldNextup(payloadFieldNextup)
      }
    }

    //  process message
    if (action === 'results-manual') {
      const payloadResultsManual = payload as SocketResultMessagePayloadResultsManual
      console.log('processSocketPayload() results-manual', payloadResultsManual)
      if (isGreaterThanCompAtSocketId) {
        socketProcessResultsManual(payloadResultsManual)
      }

      isUserFollowingEvent = ResultsService.isUserFollowingEvent(
        state.socket.followingEvents,
        payloadResultsManual.eventGroup.id
      )
      console.log(
        'isUserFollowingEvent: ' +
          isUserFollowingEvent +
          ' eventGroupId: ' +
          payloadResultsManual.eventGroup.id +
          '-' +
          payloadResultsManual.eventGroup.name
      )
      liveFeedMessageBase = isUserFollowingEvent
        ? LiveFeedService.factoryLiveFeedMessageField(action, payloadResultsManual)
        : undefined
    }

    if (action === 'field-results') {
      const payloadFieldResults = payload as SocketResultMessagePayloadFieldResults
      console.log('processSocketPayload() field-results', payloadFieldResults)
      if (isGreaterThanCompAtSocketId) {
        socketProcessFieldResults(payloadFieldResults)
      }

      isUserFollowingEvent = ResultsService.isUserFollowingEvent(
        state.socket.followingEvents,
        payloadFieldResults.ranking.eventGroup.id
      )
      console.log(
        'isUserFollowingEvent: ' +
          isUserFollowingEvent +
          ' eventGroupId: ' +
          payloadFieldResults.ranking.eventGroup.id +
          '-' +
          payloadFieldResults.ranking.eventGroup.name
      )

      liveFeedMessageBase = isUserFollowingEvent
        ? LiveFeedService.factoryLiveFeedMessageField(action, payloadFieldResults)
        : undefined
    }

    if (action === 'photofinish') {
      const payloadPhotoFinish = payload as SocketsResultsPfPayload
      console.log('processSocketPayload() photofinish', payloadPhotoFinish)
      if (isGreaterThanCompAtSocketId) {
        socketProcessPhotoResults(payloadPhotoFinish)
      }

      isUserFollowingEvent = ResultsService.isUserFollowingEvent(
        state.socket.followingEvents,
        payloadPhotoFinish.eventGroup.id
      )
      console.log(
        'isUserFollowingEvent: ' +
          isUserFollowingEvent +
          ' eventGroupId: ' +
          payloadPhotoFinish.eventGroup.id +
          '-' +
          payloadPhotoFinish.eventGroup.name
      )

      liveFeedMessageBase = isUserFollowingEvent
        ? LiveFeedService.factoryLiveFeedMessageField(action, payloadPhotoFinish)
        : undefined
    }

    if (action === 'seed-dropped') {
      const payloadSeedDropped = payload as SocketResultMessagePayloadSeedDropped
      console.log('processSocketPayload() seed-dropped', payloadSeedDropped)
      socketProcessSeedDropped(payloadSeedDropped)
    }

    if (action === 'track-move') {
      const payloadTrackMove = payload as SocketResultMessagePayloadTrackMove
      console.log('processSocketPayload() track-move', payloadTrackMove)
      socketProcessTrackMove(payloadTrackMove)
    }

    if (action === 'track-movevert') {
      const payloadTrackMoveVert = payload as SocketResultMessagePayloadTrackMoveVert
      console.log('processSocketPayload() track-movevert', payloadTrackMoveVert)
      socketProcessTrackMoveVert(payloadTrackMoveVert)
    }

    if (action === 'entries-present') {
      const payloadEntriesPresent = payload as SocketResultMessagePayloadEntriesPresent
      console.log('processSocketPayload() entries-present', payloadEntriesPresent)
      socketProcessEntriesPresent(payloadEntriesPresent)
    }

    if (action === 'seed-confirmed') {
      const payloadSeedConfirmed = payload as SocketResultMessagePayloadSeedConfirmed
      console.log('processSocketPayload() seed-confirmed', payloadSeedConfirmed)
      socketProcessSeedConfirmed(payloadSeedConfirmed)
    }

    if (action === 'entries-checkin') {
      const payloadEntriesCheckin = payload as SocketResultMessagePayloadEntriesCheckin
      console.log('processSocketPayload() entries-checkin', payloadEntriesCheckin)
      socketProcessEntriesCheckin(payloadEntriesCheckin)
    }

    // TODO
    /*
    if (liveFeedMessageBase) {
      if (liveFeedMessageServer) {
        liveFeedMessageBase.displayTime = liveFeedMessageServer.created
      }
      // addToLiveFeed(liveFeedMessageBase)
    }
    */
    //  vue reactivity...if Loading from Live feed...doing this is unnecessary.
    // if (!liveFeedMessageServer) {
    //   setScheduleToDisplay()
    // }
    // addToLiveFeed(liveFeedMessageBase)

    // if One of the sockets we are interested in Adli feed socket messages

    const isValid = RESULT_SOCKET_ACTION_TYPES.includes(action as ResultSocketActionType)

    if (isValid) {
      // const socketResultData: SocketResultData<unknown> = {
      //   action: action,
      //   domain: '',
      //   comp: state.competitionOnTheDay,
      //   payload: payload,
      //   key: '',
      //   deviceKey: ''
      // }
      addToLiveFeedSocketMessages(socketResultData)
    }
  }

  /**
   *
   * @param liveFeedMessage
   */
  function addToLiveFeed(liveFeedMessage: LiveFeedMessageBase) {
    if (!liveFeedMessage.payload) {
      return
    }
    //  Put the new message at the beginning of the array and
    //  if the new array exceeds the liveFeedQueueSize then remove the last item
    state.socket.liveFeed.unshift(liveFeedMessage)
    if (state.socket.liveFeed.length > state.socket.liveFeedQueueSize) {
      state.socket.liveFeed.pop()
    }
  }

  /**
   *
   * @param socketResultData
   */
  function addToLiveFeedSocketMessages(socketResultData: SocketResultData<unknown>) {
    if (!socketResultData.payload) {
      return
    }

    // If already in the state maps socket key then don't put it in the array
    // TODO need a way of not letting this get huge.  Only really needs to hold messages
    // TODO that were received since last full load, plus a fudge factor...10 mins?
    if (state.maps.socketsByKey[socketResultData.key]) {
      console.log(
        'addToLiveFeedSocketMessages() already in state.maps.socketsByKey: ' + socketResultData.key
      )
      return
    }
    state.maps.socketsByKey[socketResultData.key] = socketResultData

    let compEvent: CompEvent | undefined = undefined

    // Find matching event by event group ID from the state
    if (socketResultData.action === 'field-nextup') {
      //  If the soccer result data dot payload dot athlete has no keys then we have no
      //  athlete data so we don't want it in the live feed
      const payloadFieldNextup = socketResultData.payload as SocketResultMessagePayloadFieldNextup
      if (!Object.keys(payloadFieldNextup.athlete).length) {
        return
      }

      const payloadResultsManual = socketResultData.payload as SocketResultMessagePayloadFieldNextup
      const eventGroupId = payloadResultsManual.event.id
      compEvent = ResultsService.getCompetitionOnTheDayCompEventFromEgId(
        state.competitionOnTheDay,
        eventGroupId
      )
      if (!compEvent) {
        console.warn(
          'addToLiveFeedSocketMessages() No comp event found for event group id: ' + eventGroupId
        )
        return
      }
    }

    const liveFeedSocketMessage = LiveFeedService.convertSocketResultDataToLiveFeedMessage(
      socketResultData,
      compEvent
    )

    // return socketResultsData.map((socketResultData) => {
    //   return LiveFeedService.convertSocketResultDataToLiveFeedMessage(socketResultData)
    // })

    // set a guid
    // socketResultData.key = createGuid()

    //  Put the new message at the beginning of the array and
    //  if the new array exceeds the liveFeedQueueSize then remove the last item
    state.socket.liveFeedSocketMessages.unshift(liveFeedSocketMessage)
    if (state.socket.liveFeedSocketMessages.length > state.socket.liveFeedQueueSize) {
      state.socket.liveFeed.pop()
    }
  }

  /**
   *
   * @param payload
   */
  function updateTrialResults(payload: SocketResultMessagePayloadFieldResults) {
    const trialResultsUpdated = SocketFieldResultsService.updateTrialResults(
      payload,
      state.competitionOnTheDay.trialResults
    )
    state.competitionOnTheDay.trialResults = trialResultsUpdated
  }

  /**
   *
   * @param payload
   */
  function socketProcessAthleteBibNo(payload: SocketResultMessagePayloadAthleteBibNo) {
    console.log('socketProcessAthleteBibNo()', payload)

    //  Update the bibNo on the athlete in state.athletes
    const athlete = state.athletes[payload.athleteId]
    if (athlete) {
      // athlete. = payload.bibNo
    }
  }

  /**
   *
   * @param payload
   */
  function socketProcessFieldNextup(payload: SocketResultMessagePayloadFieldNextup) {
    console.log('socketProcessFieldNextup()', payload)
  }

  /**
   *
   * @param socketResultMessagePayloadResultsManual
   */
  function socketProcessResultsManual(
    socketResultMessagePayloadResultsManual: SocketResultMessagePayloadResultsManual
  ) {
    const mappedResults = SocketResultsManualService.mapResultsManual(
      socketResultMessagePayloadResultsManual
    )

    updateCompEventResults(socketResultMessagePayloadResultsManual.eventGroup.id, mappedResults)
  }

  /**
   *
   * @param socketResultMessagePayloadFieldResults
   */
  function socketProcessFieldResults(
    socketResultMessagePayloadFieldResults: SocketResultMessagePayloadFieldResults
  ) {
    const athletes = LiveFeedService.getFieldResultsAthletes(socketResultMessagePayloadFieldResults)
    const athleteIds = Object.keys(athletes)

    const mappedResults = SocketFieldResultsService.mapSocketFieldResultsResultsToIndividualResults(
      socketResultMessagePayloadFieldResults,
      athleteIds.length === 1 ? Number(athleteIds[0]) : undefined,
      athleteIds.length === 1 ? Object.values(athletes)[0] : undefined
    )

    updateCompEventResults(
      socketResultMessagePayloadFieldResults.ranking.eventGroup.id,
      mappedResults
    )

    updateTrialResults(socketResultMessagePayloadFieldResults)
  }

  /**
   *
   * @param socketsResultsPfPayload
   */
  function socketProcessPhotoResults(socketsResultsPfPayload: SocketsResultsPfPayload) {
    // Convert socket message into an actual result model that we can store against the state.
    const mappedResults =
      SocketsResultsPfService.mapSocketTrackResultsToIndividualResults(socketsResultsPfPayload)

    updateCompEventResults(socketsResultsPfPayload.eventGroup.id, mappedResults)
  }

  /**
   *
   * @param compEvent
   */
  function updateCompetitionOnTheDayEvent(compEvent: CompEvent) {
    state.competitionOnTheDay.events[compEvent.egId.toString()] = compEvent
  }

  /**
   *
   * @param compEvent
   */
  function processUpdateSelectedEvent(compEvent: CompEvent) {
    if (state.selectedEvent.compEventFiltered.egId === compEvent.egId) {
      console.log('processUpdateSelectedEvent() Updating selected event', compEvent)
      // showDetails(compEvent)
      state.selectedEvent.compEvent = CommonServiceUtils.simpleClone(compEvent)
      state.selectedEvent.compEventFiltered = CommonServiceUtils.simpleClone(compEvent)
    }
  }

  /**
   * qazzaq
   * @param payload
   */
  function socketProcessSeedDropped(payload: SocketResultMessagePayloadSeedDropped) {
    console.log('socketProcessSeedDropped()', payload)

    // get egID from payload from first entry
    const egId = payload[0].egId

    // Find start list and update the entry
    const compEvent: CompEvent | undefined = ResultsService.getCompetitionOnTheDayCompEventFromEgId(
      state.competitionOnTheDay,
      egId
    )
    if (!compEvent) {
      console.warn('socketProcessSeedDropped() No comp event found for event group id: ' + egId)
      return
    }

    const compEventOnCompetitonOnTheDay = state.competitionOnTheDay.events[egId.toString()]
    if (!compEventOnCompetitonOnTheDay) {
      console.warn(
        'socketProcessSeedDropped() No comp event found for event group id: ' +
          egId +
          ' in state.competitionOnTheDay.events'
      )
      return
    }

    const compEventUpdated = updateSeedDropped(compEvent, payload)
    updateCompetitionOnTheDayEvent(compEventUpdated)
    processUpdateSelectedEvent(compEventUpdated)
  }

  /**
   *
   * @param payload
   */
  function socketProcessTrackMove(payload: SocketResultMessagePayloadTrackMove) {
    console.log('socketProcessTrackMove()', payload)

    // using entryId of A, get from maps the egId
    const egId = state.maps.entriesByEntryId[payload.entryA.entryId.toString()]
    console.log('socketProcessTrackMove() egId: ' + egId)
    if (!egId) {
      console.warn('socketProcessTrackMove() No egId found for entryId: ' + payload.entryA.entryId)
      return
    }

    // Find start list and update the entry
    const compEvent: CompEvent | undefined = ResultsService.getCompetitionOnTheDayCompEventFromEgId(
      state.competitionOnTheDay,
      Number(egId)
    )
    if (!compEvent) {
      console.warn('socketProcessSeedDropped() No comp event found for event group id: ' + egId)
      return
    }

    const compEventUpdated = updateTrackMove(payload, compEvent)
    updateCompetitionOnTheDayEvent(compEventUpdated)
    processUpdateSelectedEvent(compEventUpdated)
  }

  /**
   *
   * @param payload
   */
  function socketProcessTrackMoveVert(payload: SocketResultMessagePayloadTrackMoveVert) {
    console.log('socketProcessTrackMoveVert()', payload)

    const compEvent: CompEvent | undefined = ResultsService.getCompetitionOnTheDayCompEventFromEgId(
      state.competitionOnTheDay,
      payload.egId
    )
    if (!compEvent) {
      console.warn(
        'socketProcessSeedDropped() No comp event found for event group id: ' + payload.egId
      )
      return
    }

    const compEventUpdated = updateTrackMoveVert(compEvent, payload)
    updateCompetitionOnTheDayEvent(compEventUpdated)
    processUpdateSelectedEvent(compEventUpdated)
  }

  /**
   *
   * @param payload
   */
  function socketProcessEntriesPresent(payload: SocketResultMessagePayloadEntriesPresent) {
    console.log('socketProcessEntriesPresent()', payload)

    // get egId from payload
    let egId: string = payload.egId.toString()

    //  N.B!!!  If egId is 0, then we need to find the egId from the entryId
    if (!egId || egId === '0') {
      console.warn('socketProcessEntriesPresent() No egId found for entryId: ' + payload.entryIds)
      // get first entryIdm, then use state m,aps to get the egId
      let firstEntryId: string = payload.entryIds[0] as any as string
      if (typeof firstEntryId === 'number') {
        firstEntryId = (firstEntryId as number).toString()
      }

      const entriesByEntryId = state.maps.entriesByEntryId

      egId = entriesByEntryId[firstEntryId]
      if (!egId) {
        console.warn('socketProcessEntriesPresent() No egId found for entryId: ' + firstEntryId)
        return
      }
    }

    const searchEgId = Number(egId)

    // Find start list and update the entry
    const compEvent: CompEvent | undefined = ResultsService.getCompetitionOnTheDayCompEventFromEgId(
      state.competitionOnTheDay,
      searchEgId
    )
    if (!compEvent) {
      console.warn('socketProcessEntriesPresent() No comp event found for event group id: ' + egId)
      return
    }

    const compEventUpdated = updateEntriesPresent(compEvent, payload)
    updateCompetitionOnTheDayEvent(compEventUpdated)
    processUpdateSelectedEvent(compEventUpdated)
  }

  /**
   *
   * @param payload
   */
  function socketProcessSeedConfirmed(payload: SocketResultMessagePayloadSeedConfirmed) {
    console.log('socketProcessSeedConfirmed()', payload)

    const egId = payload.egId

    // Find start list and update the entry
    const compEvent: CompEvent | undefined = ResultsService.getCompetitionOnTheDayCompEventFromEgId(
      state.competitionOnTheDay,
      egId
    )
    if (!compEvent) {
      console.warn('socketProcessSeedConfirmed() No comp event found for event group id: ' + egId)
      return
    }
    const compEventUpdated = updateSeedConfirmed(compEvent, payload)
    updateCompetitionOnTheDayEvent(compEventUpdated)
    processUpdateSelectedEvent(compEventUpdated)
  }

  /**
   *
   * @param payload
   */
  function socketProcessEntriesCheckin(payload: SocketResultMessagePayloadEntriesCheckin) {
    console.log('socketProcessEntriesCheckin()', payload)
    console.log('socketProcessEntriesCheckin() collected', payload.collected)

    // get first entry id and get correspoinding egId
    const firstEntryId = Object.keys(payload.checkins)[0]
    const egId = state.maps.entriesByEntryId[firstEntryId]
    if (!egId) {
      console.warn('socketProcessEntriesCheckin() No egId found for entryId: ' + firstEntryId)
      return
    }

    // Find start list and update the entry
    const compEvent: CompEvent | undefined = ResultsService.getCompetitionOnTheDayCompEventFromEgId(
      state.competitionOnTheDay,
      Number(egId)
    )
    if (!compEvent) {
      console.warn('socketProcessEntriesCheckin() No comp event found for event group id: ' + egId)
      return
    }

    const compEventUpdated = updateEntriesCheckIn(compEvent, payload)
    updateCompetitionOnTheDayEvent(compEventUpdated)
    processUpdateSelectedEvent(compEventUpdated)
  }

  /**
   *
   * @param eventGroupId
   * @param results
   * @param athleteId     If a Field result, we will get a map of athletes, it should have one entry
   *                      but if the card loses socket, it can send multiple.  we are ONLY interested
   *                      if ONE, then we can animate the result.
   */
  function updateCompEventResults(
    eventGroupId: number,
    results: Record<HeatNoString, IndividualResult[]>
  ) {
    const sourceCompEvent = ResultsService.getCompetitionOnTheDayCompEventFromEgId(
      state.competitionOnTheDay,
      eventGroupId
    )
    if (!sourceCompEvent) {
      console.warn(
        'updateCompEventResults() EXITING No comp event found for event group id: ' + eventGroupId
      )
      return
    }

    const resultsKeys = Object.keys(results)
    console.log('updateCompEventResults() resultsKeys', resultsKeys)
    if (resultsKeys.length === 0) {
      console.warn('updateCompEventResults() EXITING No results found')
      return
    }

    if (!sourceCompEvent.results) {
      sourceCompEvent.results = {}
    }

    const resultsSource: Record<HeatNoString, IndividualResult[]> = CommonServiceUtils.simpleClone(
      sourceCompEvent.results
    )

    //  loop through the results and update the source results
    for (const heatNo of resultsKeys) {
      resultsSource[heatNo] = results[heatNo]
    }

    sourceCompEvent.results = resultsSource
    //  if selected comp event is the same as the source comp event then update the selected event
    if (state.selectedEvent.compEvent.egId === sourceCompEvent.egId) {
      console.log('socketProcessFieldResults() Updating selected event')
      state.selectedEvent.compEvent = CommonServiceUtils.simpleClone(sourceCompEvent)

      // TODO ...better to just update what in filtered.. but this gets it done for now.
      state.selectedEvent.compEventFiltered = CommonServiceUtils.simpleClone(sourceCompEvent)
      searchTermChanged(state.selectedEvent.searchTerm)
    }
  }

  /**
   *
   * @param compEvent
   * @param showTab
   */
  function showDetails(
    compEvent: CompEvent,
    showTab?: 'DETAIL_ENTRIES' | 'DETAIL_START_LIST' | 'DETAIL_RESULTS'
  ) {
    state.selectedEvent.compEvent = CommonServiceUtils.simpleClone(compEvent)
    state.selectedEvent.compEventFiltered = CommonServiceUtils.simpleClone(compEvent)

    //  On selecting event, should search term be cleared???
    // state.selectedEvent.searchTerm = ''
    searchTermChanged(state.selectedEvent.searchTerm)

    state.selectedEvent.eventTimeDisplay = ResultsService.getCompEventStartTime(compEvent)
    if (compEvent.entries && Object.keys(compEvent.entries).length > 0) {
      state.selectedEvent.eventAgeGroup =
        compEvent.entries[Object.keys(compEvent.entries)[0]].eventAgeGroup
    }

    state.selectedEvent.hasEntries = !!(
      compEvent.entries && Object.keys(compEvent.entries).length > 0
    )
    state.selectedEvent.hasResults = ResultsService.compEventHasHowManyResultHeats(compEvent) > 0
    state.selectedEvent.isTrack = ResultsService.isTrackEvent(compEvent)
    state.selectedEvent.isTeam = ResultsService.isTeamEvent(compEvent)
    state.ui.showSection = 'EVENT_DETAIL'

    const forward = {
      egId: 0,
      name: ''
    }

    // has qualifyToEg
    if (compEvent.egOptions && compEvent.egOptions.seed && compEvent.egOptions.seed.qualifyToEg) {
      const qualifyToEg = compEvent.egOptions.seed.qualifyToEg

      if (typeof qualifyToEg.id === 'number' && qualifyToEg.id > 0) {
        console.warn('showDetails() qualifyToEg.id === 0')
        forward.egId = qualifyToEg.id
        // state.selectedEvent.qualify.forward.name = qualifyToEg.eventNo
        //   ? qualifyToEg.eventNo.toString()
        //   : qualifyToEg.name!
        forward.name = qualifyToEg.eventNo.toString()
      }

      // state.selectedEvent.compEvent.qualify.forward.egId = qualifyToEg.id
      // state.selectedEvent.compEvent.qualify.forward.name = qualifyToEg.name
    }

    state.selectedEvent.qualify.forward = forward

    if (showTab) {
      state.ui.eventDetail.showTab = showTab
    } else {
      state.ui.eventDetail.showTab = state.selectedEvent.hasResults
        ? 'DETAIL_RESULTS'
        : 'DETAIL_ENTRIES'
    }

    // if (showTab !== 'DETAIL_ENTRIES') {
    //   // forcing to another specific tab
    //   state.ui.eventDetail.showTab = showTab
    // } else {
    //   state.ui.eventDetail.showTab = state.selectedEvent.hasResults
    //     ? 'DETAIL_RESULTS'
    //     : 'DETAIL_ENTRIES'
    // }

    //  If user has scrolled down a long way down schedule then we need to scroll back to the top.
    window.scrollTo(0, 0)
  }

  /**
   *
   * @param searchTerm
   */
  function searchTermChanged(searchTerm: string) {
    console.log('StoreCompResults.searchTermChanged() searchTerm: ' + searchTerm)
    state.selectedEvent.searchTerm = searchTerm
    //  We need to filter the entries if there is a search term, but has to be separate copy.
    if (state.selectedEvent.searchTerm.length > 0) {
      let compEventFiltered = ResultsService.filterEntries(
        state.selectedEvent.compEvent,
        state.selectedEvent.searchTerm
      )

      compEventFiltered = ResultsService.filterResults(
        compEventFiltered,
        state.selectedEvent.searchTerm
      )

      state.selectedEvent.compEventFiltered = compEventFiltered
    } else {
      state.selectedEvent.compEventFiltered = CommonServiceUtils.simpleClone(
        state.selectedEvent.compEvent
      )
    }
  }

  /**
   *
   */
  const getSelectedCompEventTitle = computed(() => {
    return state.selectedEvent.compEvent.typeNo + ': ' + state.selectedEvent.compEvent.name
  })

  /**
   *
   */
  function destroy() {
    // Clear both timeouts
    if (pointerForSocketKeepAlive !== null) {
      clearTimeout(pointerForSocketKeepAlive)
      pointerForSocketKeepAlive = null
    }

    if (pointerForCompetitionRefresh !== null) {
      clearTimeout(pointerForCompetitionRefresh)
      pointerForCompetitionRefresh = null
    }

    // socket closed automatically
  }

  /**
   *
   * @param shouldFollowEvent
   */
  function shouldFollowEvent(shouldFollowEvent: ShouldFollowEvent) {
    const result = ResultsService.updateCurrentlyFollowing(
      state.socket.followingEvents,
      shouldFollowEvent
    )
    state.socket.followingEvents = CommonServiceUtils.simpleClone(result)
  }

  /**
   *
   */
  const followingEventCount = computed<number>(() => {
    if (!state.socket.followingEvents) {
      return 0
    }
    return Object.keys(state.socket.followingEvents).length
  })

  /**
   *
   * @param showFollowEventSetup
   */
  function setShowFollowEventSetup(showFollowEventSetup: boolean) {
    state.ui.showFollowEventSetup = showFollowEventSetup
  }

  /**
   *
   * @param eventIndex
   * @param showTab
   */
  function gotoEventIndex(
    eventIndex: number,
    showTab: 'DETAIL_ENTRIES' | 'DETAIL_START_LIST' | 'DETAIL_RESULTS' = 'DETAIL_ENTRIES'
  ) {
    console.log('gotoEVentIndex() eventIndex: ' + eventIndex)
    const compEvent = state.scheduledEvents[eventIndex]
    showDetails(compEvent, showTab)
  }

  /**
   *
   * @param egId
   */
  function getIndexOfEvent(egId: EventGroupId) {
    return state.scheduledEvents.findIndex((event) => event.egId === egId)
  }

  /**
   *
   */
  function gotoPreviousEvent() {
    console.log('gotoPreviousEvent()')

    // what is index of current event in scheduledEvents?
    // const currentIndex = state.scheduledEvents.findIndex(
    //   (event) => event.egId === state.selectedEvent.compEvent.egId
    // )

    const currentIndex = getIndexOfEvent(state.selectedEvent.compEvent.egId)
    console.log('gotoPreviousEvent() currentIndex:  ' + currentIndex)

    // get next index
    const nextIndex = currentIndex - 1
    console.log('gotoPreviousEvent() nextIndex:  ' + nextIndex)
    if (nextIndex < 0) {
      return
    }
    gotoEventIndex(nextIndex, state.ui.eventDetail.showTab)
  }

  /**
   *
   */
  function gotoNextEvent() {
    console.log('gotoNextEvent()')
    const currentIndex = getIndexOfEvent(state.selectedEvent.compEvent.egId)
    console.log('gotoNextEvent() currentIndex:  ' + currentIndex)

    // get next index
    const nextIndex = currentIndex + 1
    console.log('gotoNextEvent() nextIndex:  ' + nextIndex)
    if (nextIndex >= state.scheduledEvents.length) {
      return
    }
    gotoEventIndex(nextIndex, state.ui.eventDetail.showTab)
  }

  /**
   *
   * @param eventIndex
   * @param showTab
   */
  function gotoEvent(
    compEvent: CompEvent,
    showTab: 'DETAIL_ENTRIES' | 'DETAIL_START_LIST' | 'DETAIL_RESULTS' = 'DETAIL_ENTRIES'
  ) {
    console.log('gotoEvent() compEvent: ' + compEvent.typeNo + ': ' + compEvent.name, compEvent)

    // We need to check the compEvent is in the scheduledEvents, so switch date filter if not.
    const datePart = CommonServiceUtils.getDatePartFromIsoString(compEvent.eventDate)
    if (datePart !== state.schedule.filterDate) {
      filterScheduleDate(datePart)
    }

    showDetails(compEvent, showTab)
  }

  /**
   *
   * @param egId
   */
  function gotoQualifyingEvent(egId: EventGroupId | EventGroupIdString) {
    console.log('gotoQualifyingEvent() egId: ' + egId)
    const index = getIndexOfEvent(typeof egId === 'number' ? egId : Number(egId))
    const compEvent = state.competitionOnTheDay.events[egId.toString()]
    gotoEvent(compEvent, state.ui.eventDetail.showTab)
  }

  const getQualifyFrom = computed(() => {
    const egId = state.selectedEvent.compEvent.egId.toString()
    if (!state.maps.qualifyLinks[egId]) {
      return null
    }
    return state.maps.qualifyLinks[egId].from
  })

  const getQualifyTo = computed(() => {
    const egId = state.selectedEvent.compEvent.egId.toString()
    if (!state.maps.qualifyLinks[egId]) {
      return null
    }
    return state.maps.qualifyLinks[egId].to
  })

  /**
   *
   */
  const isSelectedEventFirst = computed(() => {
    return getIndexOfEvent(state.selectedEvent.compEvent.egId) === 0
  })

  /**
   *
   */
  const isSelectedEventLast = computed(() => {
    return getIndexOfEvent(state.selectedEvent.compEvent.egId) === state.scheduledEvents.length - 1
  })

  // is selected event a track event
  const isSelectedEventTrack = computed(() => {
    return ResultsService.isTrackEvent(state.selectedEvent.compEvent)
  })

  /**
   * N.B. this is time the competition data was last updated on the SERVER!
   * The server caches the competition data and sends it to clients.  This is different to
   * the time the client last received the data, e.g. the state.longPollDateTime.
   */
  const getCompetitionCachedTime = computed(() => {
    if (state.competitionOnTheDay && state.competitionOnTheDay.cachedDataSummary) {
      // return CommonServiceUtils.getTimePartFromIsoString(
      //   state.competitionOnTheDay.cachedDataSummary.cachedAt
      // )

      let isoString = state.competitionOnTheDay.cachedDataSummary.cachedAt

      let timePart

      // if does not contain a T, then return empty string
      if (isoString.indexOf('T') === -1) {
        return ''
      }

      if (isUtcTime(isoString)) {
        // Create date object - this automatically converts to local time
        const dateObj = new Date(isoString)

        // Format with built-in methods that respect local timezone including DST
        timePart = dateObj.toLocaleTimeString('en-GB', {
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        })
      } else {
        timePart = isoString.split('T')[1].split('.')[0]

        // take first 5 characters
        timePart = timePart.substring(0, 5)
      }

      if (timePart === 'Invalid Date') {
        return ''
      }
      if (timePart === '00:00') {
        return 'TBC'
      }
      return timePart
    }

    return 'NA'
  })

  return {
    state,
    vibrateController,
    wakeLock,
    isWakeLockOnText,
    wakeLockDebug,
    getSelectedCompEventTitle,
    debugMessages,
    followingEventCount,
    isSelectedEventFirst,
    isSelectedEventLast,
    isSelectedEventTrack,
    getCompetitionCachedTime,
    webSocket: socket,

    getQualifyFrom,
    getQualifyTo,

    init,
    getCompetitionOnTheDay,
    filterSchedule,
    filterScheduleDate,
    clearFilterSchedule,
    wakeLockCToggle,
    showDetails,
    destroy,
    testSocketMessage,
    shouldFollowEvent,
    setEventTypeFilter,
    setShowFilters,
    setShowFollowEventSetup,
    searchTermChanged,
    gotoPreviousEvent,
    gotoNextEvent,
    processSocketMessage,
    gotoQualifyingEvent,
    initSetupCompetition
  }
}
