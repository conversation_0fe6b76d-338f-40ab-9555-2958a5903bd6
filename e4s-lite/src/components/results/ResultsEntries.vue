<template>
  <!--  <div class="e4s-flex-row e4s-justify-flex-row-vert-center e4s-flex-row&#45;&#45;end">-->
  <!--    <InputDebounce-->
  <!--      class="e4s-square&#45;&#45;right e4s-flex-grow"-->
  <!--      :class="filterSearchTerm.length ? 'e4s-input-field&#45;&#45;highlight' : ''"-->
  <!--      placeholder="Search..."-->
  <!--      :value="filterSearchTerm"-->
  <!--      @input="onSearchTermChanged"-->
  <!--    />-->
  <!--    <ButtonGenericV2-->
  <!--      class="e4s-button&#45;&#45;auto"-->
  <!--      with-input="right"-->
  <!--      @click="onSearchTermClear"-->
  <!--      :disabled="filterSearchTerm.length === 0"-->
  <!--      slot="after"-->
  <!--      text="X"-->
  <!--    />-->
  <!--  </div>-->
  <div class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center">
    <div class="e4s-flex-row e4s-justify-flex-row-vert-center e4s-flex-grow">
      <InputDebounce
        class="e4s-square--right e4s-flex-grow"
        :class="filterSearchTerm.length ? 'e4s-input-field--highlight' : ''"
        placeholder="Search..."
        :value="filterSearchTerm"
        @input="onSearchTermChanged"
      />
      <ButtonGenericV2
        class="e4s-button--auto"
        with-input="right"
        @click="onSearchTermClear"
        :disabled="filterSearchTerm.length === 0"
        slot="after"
        text="X"
      />
    </div>
    <div class="e4s-flex-row--end">
      <slot name="after-search" />
    </div>
  </div>

  <div class="e4s-vertical-spacer--standard"></div>

  <!--  <div class="e4s-flex-column">-->
  <div class="e4s-flex-row">
    <div class="e4s-flex-column e4s-justify-flex-end e4s-header--500 hybrid-start-race--first-col">
      <div class="e4s-flex-row e4s-justify-flex-row-vert-center">
        Bib
        <!--        <PulseIndicator-->
        <!--          color="red"-->
        <!--          :animate="false"-->
        <!--          class="e4s-flex-row&#45;&#45;end"-->
        <!--          style="margin-right: var(&#45;&#45;e4s-gap&#45;&#45;standard)"-->
        <!--        />-->
      </div>
    </div>
    <!--    <div class="e4s-flex-row e4s-gap&#45;&#45;standard e4s-justify-flex-row-vert-center">= Athlete DNS</div>-->
    <div class="e4s-flex-column e4s-justify-flex-end e4s-flex-row--end e4s-header--500">
      Estimated
    </div>
  </div>
  <!--  </div>-->
  <div class="e4s-vertical-spacer--standard"></div>
  <hr class="dat-e4s-hr-only dat-e4s-hr--light" />
  <div>
    <HybridStartRace
      class="common-simple-row--top-only"
      v-for="individualEntry in entriesToDisplay"
      :key="individualEntry.entryId"
      :show-position="false"
      :show-bib-lane-track="false"
      :is-track="isTrack"
      :pof10="individualEntry.pof10"
      :urn="individualEntry.URN"
    >
      <template #first-col>
        <div class="e4s-flex-row e4s-gap--small e4s-justify-flex-row-vert-center">
          <span
            class="e4s-lite-number e4s-lite-number--position-lane"
            :style="individualEntry.collected !== 1 ? 'color: var(--slate-400);' : ''"
            >{{ getBibNo(individualEntry) }}</span
          >
          <!--          <PulseIndicator-->
          <!--            v-if="!individualEntry.present"-->
          <!--            color="red"-->
          <!--            :animate="false"-->
          <!--            class="e4s-flex-row&#45;&#45;end"-->
          <!--            style="margin-right: var(&#45;&#45;e4s-gap&#45;&#45;standard)"-->
          <!--          />-->
          <!--          <PulseIndicator v-if="individualEntry.collected !== 1" color="red" :animate="false" />-->
        </div>
      </template>
      <template #bibNo>
        <span class="e4s-lite-number">{{ getBibNo(individualEntry) }}</span>
      </template>
      <template #heatNo>
        {{ individualEntry.seeding.heatNo }}
      </template>
      <template #laneNo>
        {{ individualEntry.seeding.laneNo }}
      </template>
      <template #athlete-name>
        <div
          class="e4s-flex-row e4s-gap--small e4s-align-items-center e4s-justify-flex-row-vert-center e4s-lite-table--standard-text"
        >
          {{ individualEntry.firstName + ' ' + individualEntry.surName }}
          {{
            individualEntry.classification > 0 ? ' (' + individualEntry.classification + ')' : ''
          }}
        </div>
      </template>
      <template #affiliation>
        {{ individualEntry.clubName }}
      </template>
      <template #ageGroup>
        {{ '(' + individualEntry.ageGroup + ')' }}
      </template>
      <template #performance>
        <span class="e4s-lite-number e4s-lite-number--score">{{
          getScoreToDisplay(individualEntry.perf, isTrack)
        }}</span>
      </template>

      <template #sb-perf>
        {{ ResultsService.getPo10Value(individualEntry.pof10, 'SB', isTrack) }}
      </template>
      <template #pb-perf>
        {{ ResultsService.getPo10Value(individualEntry.pof10, 'PB', isTrack) }}
      </template>
      <template #po10-link>
        <PowerOfTenLink :urn="individualEntry.URN" image-height="16px" />
      </template>
      <template #extra-row> </template>
      <template #bib-checked-in>
        <div
          class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center e4s-subheader--300 e4s-subheader--general"
        >
          {{ individualEntry.checkedIn === 1 ? '' : 'Not ' }} Checked In
          <!--          <PulseIndicator-->
          <!--            :color="individualEntry.checkedIn === 1 ? 'green' : 'red'"-->
          <!--            :animate="false"-->
          <!--          />-->
        </div>
      </template>
    </HybridStartRace>
  </div>
</template>

<script setup lang="ts">
import type {
  CompEvent,
  IndividualEntry,
  StoreCompResultsState
} from '@/components/results/store-comp-results'
import { computed, type PropType, ref, watch } from 'vue'
import * as ResultsService from '@/components/results/results-service'
import PowerOfTenLink from '@/common/ui/PowerOfTenLink.vue'
import HybridStartRace from '@/components/results/HybridStartRace.vue'
import InputDebounce from '@/common/ui/fields/input-debounce.vue'
import { simpleClone } from '@/services/common-service-utils'
import { getBibNo, getScoreToDisplay } from '@/components/results/results-service'
import ButtonGenericV2 from '@/common/ui/buttons/button-generic-v2.vue'
import PulseIndicator from '@/common/ui/icons/PulseIndicator.vue'

const props = defineProps({
  storeCompResultsState: {
    type: Object as PropType<StoreCompResultsState>,
    required: true
  },
  scheduledEvent: {
    type: Object as PropType<CompEvent>,
    required: true
  },
  filterSearchTerm: {
    type: String,
    default: ''
  }
})

const emit = defineEmits<{
  (e: 'searchTermChanged', value: string): void
}>()

watch(
  () => props.scheduledEvent,
  (newVal, oldValue) => {
    console.log('ResultEntries.scheduledEvent changed', newVal, oldValue)
    init(newVal, props.filterSearchTerm)
  }
)

watch(
  () => props.filterSearchTerm,
  (newVal, oldValue) => {
    if (newVal !== filterTerm.value) {
      console.log('ResultEntries.filterSearchTerm changed', newVal, oldValue)
      filterTerm.value = newVal
    }
  }
)

const filterTerm = ref('')
const entriesToDisplay = ref<IndividualEntry[]>([])

console.log('ResultEntries.setup() props.scheduledEvent', props.scheduledEvent)
// filterEntries()

init(props.scheduledEvent, props.filterSearchTerm)

function init(scheduledEvent: CompEvent, searchTerm: string) {
  console.log('ResultEntries.init()')
  filterTerm.value = searchTerm
  const compEvent = simpleClone(scheduledEvent)

  let entriesToDisplayLocal = compEvent.entries ? Object.values(compEvent.entries) : []

  entriesToDisplayLocal = ResultsService.sortEntriesByEstimatedPerformance(
    entriesToDisplayLocal,
    ResultsService.isTrackEvent(compEvent)
  )

  entriesToDisplay.value = entriesToDisplayLocal
}

function filterEntries(searchTerm: string = '') {
  console.log('ResultEntries.filterEntries searchTerm: ' + searchTerm + '...DISABLED')
  // let compEvent = simpleClone(props.scheduledEvent)
  // filterTerm.value = searchTerm
  // if (compEvent.entries) {
  //   if (searchTerm.length > 0) {
  //     compEvent = ResultsService.filterEntries(props.scheduledEvent, searchTerm)
  //   }
  //   entriesToDisplay.value = compEvent.entries ? Object.values(compEvent.entries) : []
  // }
  // const compEvent = simpleClone(props.scheduledEvent)
  // entriesToDisplay.value = compEvent.entries ? Object.values(compEvent.entries) : []
}

function onSearchTermClear() {
  emit('searchTermChanged', '')
}

function onSearchTermChanged(searchTerm: string) {
  console.log('ResultEntries.onSearchTermChanged', searchTerm)
  emit('searchTermChanged', searchTerm)
}

const isTrack = computed(() => {
  return ResultsService.isTrackEvent(props.scheduledEvent)
})
</script>
