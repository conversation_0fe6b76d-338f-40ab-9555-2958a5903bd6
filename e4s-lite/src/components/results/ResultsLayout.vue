<template>
  <div class="e4s-flex-column">
    <div class="e4s-flex-column">
      <div class="e4s-top-nav e4s-flex-row">
        <SectionLinkSimple
          link-title="Schedule"
          :is-active="storeCompResults.state.ui.showSection === 'SCHEDULE'"
          @selected="storeCompResults.state.ui.showSection = 'SCHEDULE'"
        >
          <div class="e4s-header--400 e4s-justify-flex-row-vert-center">Schedule</div>
        </SectionLinkSimple>

        <SectionLinkSimple
          v-if="storeCompResults.state.ui.showSection === 'EVENT_DETAIL'"
          :is-active="storeCompResults.state.ui.showSection === 'EVENT_DETAIL'"
          @selected="storeCompResults.state.ui.showSection === 'EVENT_DETAIL'"
        >
          <div class="e4s-header--400 e4s-justify-flex-row-vert-center">Details</div>
        </SectionLinkSimple>

        <SectionLinkSimple
          :is-active="storeCompResults.state.ui.showSection === 'LIVE_FEED'"
          @selected="storeCompResults.state.ui.showSection = 'LIVE_FEED'"
        >
          <div class="e4s-header--400 e4s-justify-flex-row-vert-center">
            Live Feed
            <!--            <Lightning v-if="storeCompResults.state.socket.isConnected" />-->
            <!--            <LightningSlash v-if="!storeCompResults.state.socket.isConnected" />-->
            <PulseIndicator
              color="var(--socket-inactive-color)"
              :animate="false"
              v-if="!storeCompResults.state.socket.isConnected"
            />
            <PulseIndicator
              color="var(--socket-active-color)"
              :animate="true"
              v-if="storeCompResults.state.socket.isConnected"
            />
          </div>
        </SectionLinkSimple>

        <SectionLinkSimple link-title="111" :is-active="false" class="e4s-flex-row--end">
          <span
            v-text="storeCompResults.getCompetitionCachedTime.value"
            class="e4s-subheader--general"
          ></span>
        </SectionLinkSimple>
      </div>
      <ul
        class="e4s-sub-nav e4s-flex-row"
        v-if="storeCompResults.state.ui.showSection === 'EVENT_DETAIL'"
      >
        <SubNavLink link-title="Details >" class="e4s-sub-nav--inert" />

        <SubNavLink
          link-title="Entries"
          :is-active="storeCompResults.state.ui.eventDetail.showTab === 'DETAIL_ENTRIES'"
          @selected="storeCompResults.state.ui.eventDetail.showTab = 'DETAIL_ENTRIES'"
        />

        <li class="e4s-sub-nav-link--sep">|</li>

        <SubNavLink
          link-title="Start List"
          :is-disabled="storeCompResults.state.selectedEvent.isTeam"
          :is-active="storeCompResults.state.ui.eventDetail.showTab === 'DETAIL_START_LIST'"
          @selected="storeCompResults.state.ui.eventDetail.showTab = 'DETAIL_START_LIST'"
        />

        <li class="e4s-sub-nav-link--sep">|</li>

        <SubNavLink
          link-title="Results"
          :is-active="storeCompResults.state.ui.eventDetail.showTab === 'DETAIL_RESULTS'"
          @selected="storeCompResults.state.ui.eventDetail.showTab = 'DETAIL_RESULTS'"
        />
      </ul>
    </div>

    <div class="e4s-flex-column e4s-gap--standard">
      <div
        class="e4s-flex-column e4s-gap--standard"
        v-if="storeCompResults.state.isLoading || !storeCompResults.state.isReady"
      >
        <LoadingSpinnerV2 :loading-message="storeCompResults.state.loadingMessage" />
      </div>
      <!--    <ResultsCompHeader-->
      <!--      :competition-on-the-day="storeCompResults.state.competitionOnTheDay"-->
      <!--      v-if="!isEmbedded"-->
      <!--    />-->

      <div class="e4s-content-wrapper-new">
        <!--        <ResultsCompHeader-->
        <!--          :competition-on-the-day="storeCompResults.state.competitionOnTheDay"-->
        <!--          v-if="!isEmbedded"-->
        <!--        />-->

        <div v-if="storeCompResults.state.isReady">
          <!--SCHEDULE-->
          <div
            v-show="storeCompResults.state.ui.showSection === 'SCHEDULE'"
            class="e4s-flex-column"
          >
            <div class="e4s-vertical-spacer--standard"></div>

            <!--Filters-->
            <!--        v-if="!storeCompResults.state.ui.showFollowEventSetup"-->
            <div>
              <div class="e4s-flex-row e4s-gap--standard e4s-justify-flex-space-between">
                <FormGenericInputTemplate
                  form-label="Date"
                  :show-label="false"
                  v-if="storeCompResults.state.dates.length > 1"
                >
                  <template #field>
                    <FieldSelect
                      :class="
                        storeCompResults.state.schedule.filterIsEnabled.date
                          ? 'e4s-input-field--highlight'
                          : ''
                      "
                      :data-array="storeCompResults.state.dates"
                      :value="storeCompResults.state.schedule.filterDate"
                      @input="storeCompResults.filterScheduleDate"
                    >
                      <template #default="{ obj }">
                        <span v-text="formatDateWithSuffix(obj as string)"></span>
                      </template>
                    </FieldSelect>
                  </template>
                </FormGenericInputTemplate>

                <FormGenericInputTemplate form-label="Type" :show-label="false">
                  <template #field>
                    <EventTypeSelect
                      :class="
                        storeCompResults.state.schedule.filterIsEnabled.eventType
                          ? 'e4s-input-field--highlight'
                          : ''
                      "
                      :value="storeCompResults.state.schedule.filterEventType"
                      :event-types="storeCompResults.state.schedule.eventTypesAvailable"
                      @input="storeCompResults.setEventTypeFilter"
                    />
                  </template>
                </FormGenericInputTemplate>
              </div>

              <FormGenericInputTemplate
                form-label="Search"
                :show-label="false"
                class="e4s-flex-grow"
              >
                <template #field>
                  <div class="e4s-flex-row e4s-justify-flex-row-vert-center e4s-flex-grow">
                    <InputDebounce
                      class="e4s-square--right e4s-flex-grow"
                      :class="
                        storeCompResults.state.schedule.filterIsEnabled.term
                          ? 'e4s-input-field--highlight'
                          : ''
                      "
                      :value="storeCompResults.state.schedule.filterTerm"
                      @input="storeCompResults.filterSchedule"
                      placeholder="Filter..."
                    />
                    <ButtonGenericV2
                      class="e4s-button--auto"
                      with-input="right"
                      @click="storeCompResults.clearFilterSchedule"
                      :disabled="storeCompResults.state.schedule.filterTerm.length === 0"
                      slot="after"
                      text="X"
                    />
                  </div>
                </template>
              </FormGenericInputTemplate>
            </div>
            <!--/Filters-->

            <div class="e4s-vertical-spacer--standard"></div>
            <div class="e4s-flex-row">
              <CollapseActivator
                class="e4s-flex-row--end"
                :show-left="false"
                @isOpen="storeCompResults.setShowFollowEventSetup"
              >
                <template #title-slot>
                  <div class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center">
                    <PulseIndicator
                      v-if="storeCompResults.followingEventCount.value > 0"
                      color="var(--socket-active-color)"
                      :animate="true"
                    />
                    <span
                      class="e4s-subheader--300xxx"
                      style="font-weight: bold"
                      :style="storeCompResults.state.ui.showFollowEventSetup ? 'color: red;' : ''"
                      v-text="
                        'Following events' +
                        (storeCompResults.followingEventCount.value > 0
                          ? ' (' + storeCompResults.followingEventCount.value + ')'
                          : '')
                      "
                    ></span>
                  </div>
                </template>
              </CollapseActivator>

              <!--          <div-->
              <!--            class="e4s-flex-row e4s-flex-row&#45;&#45;end"-->
              <!--            style="font-weight: bold"-->
              <!--            v-if="!storeCompResults.state.ui.showFollowEventSetup"-->
              <!--          >-->
              <!--            <TickIcon /> = Results available-->
              <!--          </div>-->
            </div>

            <!--Following-->
            <div
              v-if="storeCompResults.state.ui.showFollowEventSetup"
              class="e4s-flex-column e4s-gap--standard"
            >
              <div class="e4s-flex-column e4s-gap--large">
                <div class="e4s-flex-column e4s-gap--standard">
                  <div class="e4s-vertical-spacer--standard"></div>
                  <p>
                    Follow specific event(s) below and receive notifications when a result comes in
                    on the <span class="e4s-header--500">Live Feed</span>.
                  </p>
                  <p>None selected will default to live notifications for all events.</p>
                </div>
              </div>
              <!--          <FollowingWithButtons-->
              <!--            v-if="storeCompResults.followingEventCount.value > 0"-->
              <!--            :following-events="storeCompResults.state.socket.followingEvents"-->
              <!--            @shouldFollowEvent="storeCompResults.shouldFollowEvent"-->
              <!--          />-->
              <!--          <hr class="dat-e4s-hr-only dat-e4s-hr&#45;&#45;light" />-->
            </div>
            <!--/Following-->

            <div class="e4s-vertical-spacer--large"></div>

            <hr class="dat-e4s-hr-only" />

            <ResultsSchedule
              :scheduled-events="storeCompResults.state.scheduledEvents"
              :show-follow-event-setup="storeCompResults.state.ui.showFollowEventSetup"
              :following-events="storeCompResults.state.socket.followingEvents"
              @show-details="storeCompResults.showDetails"
              @shouldFollowEvent="storeCompResults.shouldFollowEvent"
            />
          </div>
          <!--/SCHEDULE-->

          <!--LIVE_FEED-->
          <div
            v-show="storeCompResults.state.ui.showSection === 'LIVE_FEED'"
            class="e4s-flex-column"
          >
            <div class="e4s-vertical-spacer--standard"></div>
            <FollowingWithButtons
              :following-events="storeCompResults.state.socket.followingEvents"
              @shouldFollowEvent="storeCompResults.shouldFollowEvent"
            >
              <template #change-following>
                <PrimaryLink
                  link-text="Change"
                  @click="storeCompResults.state.ui.showSection = 'SCHEDULE'"
                />
              </template>
            </FollowingWithButtons>
            <!--            <span class="e4s-subheader&#45;&#45;general">-->
            <!--              Change events following-->
            <!--              <PrimaryLink-->
            <!--                link-text="schedule tab"-->
            <!--                @click="storeCompResults.state.ui.showSection = 'SCHEDULE'"-->
            <!--              />-->
            <!--            </span>-->
            <div class="e4s-vertical-spacer--standard"></div>
            <!--          <LiveFeed-->
            <!--            :live-feed="storeCompResults.state.socket.liveFeed"-->
            <!--            :following-events="storeCompResults.state.socket.followingEvents"-->
            <!--          />-->
            <LiveFeedSockets
              :store-comp-results-state="storeCompResults.state"
              :live-feed="storeCompResults.state.socket.liveFeedSocketMessages"
              :following-events="storeCompResults.state.socket.followingEvents"
            />
          </div>
          <!--/LIVE_FEED-->

          <!--EVENT_DETAIL-->
          <div v-if="storeCompResults.state.ui.showSection === 'EVENT_DETAIL'">
            <div class="e4s-flex-column e4s-gap--standard">
              <div class="e4s-flex-row">
                <span
                  class="e4s-header--400"
                  v-text="storeCompResults.getSelectedCompEventTitle.value"
                ></span>

                <span
                  class="e4s-header--400 e4s-flex-row--end"
                  v-text="'@' + storeCompResults.state.selectedEvent.eventTimeDisplay"
                ></span>
              </div>

              <div class="e4s-flex-row">
                <span
                  class="e4s-header--500 e4s-subheader--general"
                  v-text="storeCompResults.state.selectedEvent.eventAgeGroup"
                ></span>

                <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
                  <ButtonGenericV2
                    v-if="storeCompResults.getQualifyFrom.value"
                    button-type="primary"
                    class="e4s-button--slim e4s-button--prev-next"
                    :text="'<< ' + storeCompResults.getQualifyFrom.value.name"
                    @onButtonClicked="
                      storeCompResults.gotoQualifyingEvent(
                        storeCompResults.getQualifyFrom.value.egId.toString()
                      )
                    "
                  />

                  <ButtonGenericV2
                    v-if="storeCompResults.getQualifyTo.value"
                    button-type="primary"
                    class="e4s-button--slim e4s-button--prev-next"
                    :text="storeCompResults.getQualifyTo.value.name + ' >>'"
                    @onButtonClicked="
                      storeCompResults.gotoQualifyingEvent(
                        storeCompResults.getQualifyTo.value.egId.toString()
                      )
                    "
                  />

                  <!--                  <ButtonGenericV2-->
                  <!--                    class="e4s-button&#45;&#45;slim e4s-button&#45;&#45;prev-next"-->
                  <!--                    button-type="primary"-->
                  <!--                    text="Prev"-->
                  <!--                    :disabled="storeCompResults.isSelectedEventFirst.value"-->
                  <!--                    @onButtonClicked="storeCompResults.gotoPreviousEvent"-->
                  <!--                  />-->
                  <!--                  <ButtonGenericV2-->
                  <!--                    class="e4s-button&#45;&#45;slim e4s-button&#45;&#45;prev-next"-->
                  <!--                    button-type="primary"-->
                  <!--                    text="Next"-->
                  <!--                    :disabled="storeCompResults.isSelectedEventLast.value"-->
                  <!--                    @onButtonClicked="storeCompResults.gotoNextEvent"-->
                  <!--                  />-->
                </div>
              </div>
            </div>

            <template v-if="storeCompResults.state.ui.eventDetail.showTab === 'DETAIL_ENTRIES'">
              <ResultsEntries
                :store-comp-results-state="storeCompResults.state"
                :scheduled-event="storeCompResults.state.selectedEvent.compEventFiltered"
                :filter-search-term="storeCompResults.state.selectedEvent.searchTerm"
                @searchTermChanged="storeCompResults.searchTermChanged"
                v-if="!storeCompResults.state.selectedEvent.isTeam"
              >
                <template #after-search>
                  <div
                    class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center"
                    :style="{ visibility: storeCompResults.isSelectedEventTrack.value ? 'visible' : 'hidden' }"
                  >
                    <ButtonGenericV2
                      class="e4s-button--prev-next"
                      button-type="primary"
                      text="Prev"
                      :disabled="storeCompResults.isSelectedEventFirst.value"
                      @onButtonClicked="storeCompResults.gotoPreviousEvent"
                    />
                    <ButtonGenericV2
                      class="e4s-button--prev-next"
                      button-type="primary"
                      text="Next"
                      :disabled="storeCompResults.isSelectedEventLast.value"
                      @onButtonClicked="storeCompResults.gotoNextEvent"
                    />
                  </div>
                </template>
              </ResultsEntries>
              <ResultsTeamEntries
                :store-comp-results-state="storeCompResults.state"
                :scheduled-event="storeCompResults.state.selectedEvent.compEvent"
                @searchTermChanged="storeCompResults.searchTermChanged"
                v-if="storeCompResults.state.selectedEvent.isTeam"
              />
            </template>

            <template v-if="storeCompResults.state.ui.eventDetail.showTab === 'DETAIL_START_LIST'">
              <StartList
                :store-comp-results-state="storeCompResults.state"
                :comp-event="storeCompResults.state.selectedEvent.compEventFiltered"
                @searchTermChanged="storeCompResults.searchTermChanged"
                :filter-search-term="storeCompResults.state.selectedEvent.searchTerm"
              >
                <template #after-search>
                  <div
                    class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center"
                    :style="{ visibility: storeCompResults.isSelectedEventTrack.value ? 'visible' : 'hidden' }"
                  >
                    <ButtonGenericV2
                      class="e4s-button--prev-next"
                      button-type="primary"
                      text="Prev"
                      :disabled="storeCompResults.isSelectedEventFirst.value"
                      @onButtonClicked="storeCompResults.gotoPreviousEvent"
                    />
                    <ButtonGenericV2
                      class="e4s-button--prev-next"
                      button-type="primary"
                      text="Next"
                      :disabled="storeCompResults.isSelectedEventLast.value"
                      @onButtonClicked="storeCompResults.gotoNextEvent"
                    />
                  </div>
                </template>
              </StartList>
              <!--TeamsStartList...when available-->
            </template>

            <!--          storeCompResults.state.selectedEvent.compEvent.egId-->
            <!--          {{ storeCompResults.state.selectedEvent.compEvent.egId }}-->
            <HeatResults
              :store-comp-results-state="storeCompResults.state"
              :scheduled-event="storeCompResults.state.selectedEvent.compEventFiltered"
              :filter-search-term="storeCompResults.state.selectedEvent.searchTerm"
              :event-trial-map="
                storeCompResults.state.competitionOnTheDay.trialResults[
                  storeCompResults.state.selectedEvent.compEvent.egId
                ]
              "
              @searchTermChanged="storeCompResults.searchTermChanged"
              @gotoQualifyingEvent="storeCompResults.gotoQualifyingEvent"
              v-if="storeCompResults.state.ui.eventDetail.showTab === 'DETAIL_RESULTS'"
            >
              <template #after-search>
                <div
                  class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center"
                  :style="{ visibility: storeCompResults.isSelectedEventTrack.value ? 'visible' : 'hidden' }"
                >
                  <ButtonGenericV2
                    class="e4s-button--prev-next"
                    button-type="primary"
                    text="Prev"
                    :disabled="storeCompResults.isSelectedEventFirst.value"
                    @onButtonClicked="storeCompResults.gotoPreviousEvent"
                  />
                  <ButtonGenericV2
                    class="e4s-button--prev-next"
                    button-type="primary"
                    text="Next"
                    :disabled="storeCompResults.isSelectedEventLast.value"
                    @onButtonClicked="storeCompResults.gotoNextEvent"
                  />
                </div>
              </template>
            </HeatResults>
          </div>
          <!--/EVENT_DETAIL-->
          <!--      </template>-->
        </div>
      </div>
    </div>

    <SocketDebugger v-if="false" />
  </div>
</template>

<script setup lang="ts">
import ResultsSchedule from '@/components/results/schedule/ResultsSchedule.vue'

const props = defineProps({
  compId: {
    type: Number,
    required: true
  },
  isEmbedded: {
    type: Boolean,
    default: false
  }
})
import { useStoreCompResults } from '@/components/results/store-comp-results'
import { onBeforeUnmount } from 'vue'
import ResultsEntries from '@/components/results/ResultsEntries.vue'
import HeatResults from '@/components/results/HeatResults.vue'
import ResultsCompHeader from '@/components/results/ResultsCompHeader.vue'
import SectionLinkSimple from '@/common/ui/tabs/section-link-simple.vue'
import StartList from '@/components/results/start-list/StartList.vue'
import InputDebounce from '@/common/ui/fields/input-debounce.vue'
import LoadingSpinnerV2 from '@/common/ui/loading-spinner-v2.vue'
import ResultsTeamEntries from '@/components/results/teams/ResultsTeamEntries.vue'
import LiveFeed from '@/components/results/socket/live-feed/LiveFeed.vue'
import ButtonGenericV2 from '@/common/ui/buttons/button-generic-v2.vue'
import PrimaryLink from '@/common/ui/href/PrimaryLink.vue'
import FollowingWithButtons from '@/components/results/socket/following/FollowingWithButtons.vue'
import TickIcon from '@/common/ui/svg/TickIcon.vue'
import FieldSelect from '@/common/ui/fields/FieldSelect.vue'
import { formatDateWithSuffix } from '@/services/common-service-utils'
import EventTypeSelect from '@/common/ui/fields/EventTypeSelect.vue'
import CollapseActivator from '@/common/ui/collapse/CollapseActivator.vue'
import FormGenericFieldGrid from '@/common/ui/form/FormGenericFieldGrid.vue'
import FormGenericInputTemplate from '@/common/ui/form/FormGenericInputTemplate.vue'
import Lightning from '@/common/ui/svg/Lightning.vue'
import LightningSlash from '@/common/ui/svg/LightningSlash.vue'
import StopSign from '@/common/ui/svg/StopSign.vue'
import LiveFeedSockets from '@/components/results/socket/live-feed/ui/LiveFeedSockets.vue'
import SubNavLink from '@/common/ui/tabs/sub-nav-link.vue'
import PulseIndicator from '@/common/ui/icons/PulseIndicator.vue'
import SocketDebugger from '@/components/results/admin/ui/SocketDebugger.vue'

const storeCompResults = useStoreCompResults()
storeCompResults.init(props.compId)

onBeforeUnmount(() => {
  console.log('ResultsLayout onBeforeUnmount')
  storeCompResults.destroy()
})
</script>

<style>
.test-anime {
  height: auto;
  transition: height 5s ease-in;
}

.stretchy {
  transition: 1s linear; /* Adjust the duration as needed */
  height: 0;
  overflow: hidden;
}

.stretchy-visible {
  height: auto;
  min-height: 40px; /* Set a minimum height */
  max-height: 400px; /* Set a maximum height (larger than your content) */
}
</style>
