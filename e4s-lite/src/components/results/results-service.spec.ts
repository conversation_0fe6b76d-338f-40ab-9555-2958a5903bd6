import { describe, expect, it } from 'vitest'
import { isNumeric, simpleClone } from '@/services/common-service-utils'
import { mockCompData } from '@/components/results/results-mock/results-mock-data'
import type { CompEvent, IndividualEntry } from '@/components/results/store-comp-results'
import * as ResultsService from '@/components/results/results-service'
import { socketMessageResultsManual } from '@/components/results/results-mock/socket-mocks'
import type {
  ResultSocketActionType,
  SocketResultData,
  SocketShouldMessageBeAcceptedResult
} from '@/components/results/socket/result-socket-models'
import { shouldSocketMessageBeAccepted } from '@/components/results/socket/socket-service'
import type { SocketResultMessagePayloadResultsManual } from '@/components/results/socket/socket-results-manual/socket-results-manual-models'
import {
  indivResultsMockNew,
  indivResultsMockOrig
} from '@/components/results/results-mock/animation-mock'
import {
  athleteReorderDetails,
  cleanUpScoreValueForDisplay,
  createQualifyToFromMap,
  filterLiveFeedShowOnlyFollowing,
  formatTrackScore
} from '@/components/results/results-service'
import {
  followingMock,
  liveFeedMock
} from '@/components/results/results-mock/live-feed/live-feed-mock'
import { mockCompEventCheckIn } from '@/components/results/mocks/comps/617/mockCompEventCheckIn'
import { mockScheduleForQualifyLinks } from '@/components/results/mocks/comps/617/mockScheduleForQualifyLinks'
import { mockScheduleForSorting } from '@/components/results/mocks/comps/617/mockScheduleForSorting'

describe('Results Service', () => {
  // it('sortSchedule', () => {
  //   const data = simpleClone(mockCompData)
  //
  //   let scheduledEvents: CompEvent[] = Object.values(data.events)
  //
  //   expect(scheduledEvents.length).toBe(5)
  //
  //   scheduledEvents = ResultsService.sortSchedule(scheduledEvents, 'eventNo')
  //
  //   expect(scheduledEvents[0].typeNo).toBe('T1')
  //   expect(scheduledEvents[1].typeNo).toBe('T3')
  //   expect(scheduledEvents[2].typeNo).toBe('T5')
  //   expect(scheduledEvents[3].typeNo).toBe('T6')
  //   expect(scheduledEvents[4].typeNo).toBe('F7')
  // })

  it('doesDomainMatch', () => {
    expect(
      ResultsService.doesDomainMatch('dev.entry4sports.co.uk', ['dev.entry4sports.co.uk'])
    ).toBe(true)

    expect(
      ResultsService.doesDomainMatch('https://dev.entry4sports.co.uk', ['dev.entry4sports.co.uk'])
    ).toBe(true)

    expect(
      ResultsService.doesDomainMatch('dev.entry4sports.co.uk', ['dev.entry4sports.co.ukx'])
    ).toBe(false)

    expect(
      ResultsService.doesDomainMatch('dev.entry4sports.co.uk', ['dev.entry4sports.co.ukx'])
    ).toBe(false)
  })

  it('shouldSocketMessageBeAccepted', () => {
    let result: SocketShouldMessageBeAcceptedResult
    const socketMessage: SocketResultData<SocketResultMessagePayloadResultsManual> = simpleClone(
      socketMessageResultsManual
    ) as any as SocketResultData<SocketResultMessagePayloadResultsManual>

    result = shouldSocketMessageBeAccepted(socketMessage, {
      listenForCompIds: [999],
      listenForMessageDomains: ['dev.entry4sports.co.uk-xxx'],
      listenForMessageTypes: ['results-manual-x' as ResultSocketActionType]
    })

    expect(result.should).toBe(false)
    expect(result.identifier).toBe('DOMAIN_NO_MATCH')

    result = shouldSocketMessageBeAccepted(socketMessage, {
      listenForCompIds: [421],
      listenForMessageDomains: [''],
      listenForMessageTypes: []
    })

    expect(result.should).toBe(false)
    expect(result.identifier).toBe('DOMAIN_NO_MATCH')

    result = shouldSocketMessageBeAccepted(socketMessage, {
      listenForCompIds: [421],
      listenForMessageDomains: ['dev.entry4sports.co.uk'],
      listenForMessageTypes: ['results-manual-x' as ResultSocketActionType]
    })

    expect(result.should).toBe(false)
    expect(result.identifier).toBe('MESSAGE_TYPE_NO_MATCH')

    result = shouldSocketMessageBeAccepted(socketMessage, {
      listenForCompIds: [421],
      listenForMessageDomains: ['dev.entry4sports.co.uk'],
      listenForMessageTypes: ['results-manual']
    })

    expect(result.should).toBe(true)
    expect(result.identifier).toBe('OK')
  })

  it('getCompEventStartTime', () => {
    expect(
      ResultsService.getCompEventStartTime({
        eventDate: '2024-03-31T00:00:00'
      } as CompEvent)
    ).toBe('TBC')

    expect(
      ResultsService.getCompEventStartTime({
        eventDate: '2024-03-31T01:00:00'
      } as CompEvent)
    ).toBe('01:00')

    expect(
      ResultsService.getCompEventStartTime({
        eventDate: '2024-03-31T23:34:00'
      } as CompEvent)
    ).toBe('23:34')

    expect(
      ResultsService.getCompEventStartTime({
        eventDate: '2024-03-31T01:00:00'
      } as CompEvent)
    ).toBe('01:00')

    expect(
      ResultsService.getCompEventStartTime({
        eventDate: '2024-04-29T01:00:00+01:00'
      } as CompEvent)
    ).toBe('01:00')

    expect(
      ResultsService.getCompEventStartTime({
        eventDate: '2024-04-29T01:00:00+01:00'
      } as CompEvent)
    ).toBe('01:00')

    expect(
      ResultsService.getCompEventStartTime({
        eventDate: '2024-01-29T03:00:00Z'
      } as CompEvent)
    ).toBe('03:00')

    expect(
      ResultsService.getCompEventStartTime({
        eventDate: '2024-06-29T03:00:00Z'
      } as CompEvent)
    ).toBe('04:00')
  })

  it('doesDomainMatch', () => {
    const indiv: IndividualEntry = {
      entryId: 137408,
      athleteId: 166186,
      firstName: 'Grace',
      surName: 'McCollin',
      aoCode: 'EA',
      URN: '3992867',
      perf: 11.24,
      pof10: { pbPerf: 11.13, pbAchieved: '2023-06-30 00:00:00', sbPerf: '12.4', sbAchieved: null },
      eventAgeGroup: 'ES Juniors',
      ageGroup: 'ES Juniors',
      bibNo: '1007',
      teamBibNo: '81',
      clubName: 'Warwickshire',
      gender: 'F',
      seeding: { heatNo: 2, laneNo: 4 },
      present: 1,
      checkedIn: 1,
      collected: 1,
      heatNoCheckedIn: 1,
      laneNoCheckedIn: 1,
      classification: 0
    }

    expect(ResultsService.hasPo10Value(indiv.pof10, 'PB')).toBe(true)
    expect(
      ResultsService.hasPo10Value(
        { pbPerf: 11.13, pbAchieved: '2023-06-30 00:00:00', sbPerf: '12.4', sbAchieved: null },
        'SB'
      )
    ).toBe(true)

    expect(
      ResultsService.hasPo10Value(
        { pbPerf: 11.13, pbAchieved: '2023-06-30 00:00:00', sbPerf: '', sbAchieved: null },
        'SB'
      )
    ).toBe(false)

    expect(
      ResultsService.hasPo10Value(
        { pbPerf: 11.13, pbAchieved: '2023-06-30 00:00:00', sbPerf: null, sbAchieved: null },
        'SB'
      )
    ).toBe(false)

    expect(
      ResultsService.hasPo10Value(
        { pbPerf: 11.13, pbAchieved: '2023-06-30 00:00:00', sbPerf: '0', sbAchieved: null },
        'SB'
      )
    ).toBe(false)

    expect(ResultsService.getPo10Value(indiv.pof10, 'PB', true)).toBe('11.13')
    expect(ResultsService.getPo10Value(indiv.pof10, 'SB', true)).toBe('12.40')

    // indiv.pof10!.sbPerf = null
    // expect(ResultsService.getPo10(indiv, 'SB')).toBe('-')
  })

  it('mapResultsManual', () => {
    const result = ResultsService.getCompetitionDates(simpleClone(mockCompData))

    expect(result.length).toBe(2)
  })

  it('mapResultsManual', () => {
    let result
    const originalResults = simpleClone(indivResultsMockOrig)
    const newResults = simpleClone(indivResultsMockNew)

    result = ResultsService.athleteReorderDetails(originalResults, newResults, 0)
    expect(result.hasChangedPosition).toBe(false)
    expect(result.hasScoreChanged).toBe(false)

    result = ResultsService.athleteReorderDetails(originalResults, newResults, 155885)
    expect(result.hasChangedPosition).toBe(true)
    expect(result.originalIndex).toBe(5)
    expect(result.newIndex).toBe(2)
    expect(result.originalPosition).toBe(6)
    expect(result.newPosition).toBe(3)
    expect(result.hasScoreChanged).toBe(true)
    expect(result.originalScore).toBe('2.14')
    expect(result.newScore).toBe('3.65')

    // 111111
    result = ResultsService.athleteReorderDetails(originalResults, newResults, 111111)
    expect(result.hasChangedPosition).toBe(true)
    expect(result.hasScoreChanged).toBe(true)
    expect(result.originalIndex).toBe(-1)
    expect(result.newIndex).toBe(13)
    expect(result.originalPosition).toBe(-1)
    expect(result.newPosition).toBe(14)
    expect(result.originalScore).toBe('')
    expect(result.newScore).toBe('1.22')
  })

  it('turnSecondsIntoHumanReadableTime', () => {
    expect(ResultsService.turnSecondsIntoHumanReadableTime(11)).toBe('11.00')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(9)).toBe('9.00')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(9.1)).toBe('9.10')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(9.12)).toBe('9.12')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(9.123)).toBe('9.12')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(24.123)).toBe('24.12')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(59.04)).toBe('59.04')

    expect(ResultsService.turnSecondsIntoHumanReadableTime(64.12)).toBe('1:04.12')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(74.12)).toBe('1:14.12')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(74.1)).toBe('1:14.10')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(94)).toBe('1:34.00')

    expect(ResultsService.turnSecondsIntoHumanReadableTime(599)).toBe('9:59.00')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(599)).toBe('9:59.00')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(600)).toBe('10:00.00')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(601.01)).toBe('10:01.01')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(601.1)).toBe('10:01.10')

    expect(ResultsService.turnSecondsIntoHumanReadableTime(671.1)).toBe('11:11.10')

    expect(ResultsService.turnSecondsIntoHumanReadableTime(671.1)).toBe('11:11.10')

    expect(ResultsService.turnSecondsIntoHumanReadableTime(3600)).toBe('1:00:00')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(3601)).toBe('1:00:01')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(3601.1)).toBe('1:00:01')
    expect(ResultsService.turnSecondsIntoHumanReadableTime(3661.1)).toBe('1:01:01')
  })

  it('getScoreToDisplay', () => {
    const isTrackEvent = false
    expect(ResultsService.getScoreToDisplay(3600, isTrackEvent)).toBe('3600.00')
    expect(ResultsService.getScoreToDisplay(1.34, isTrackEvent)).toBe('1.34')

    expect(''.padEnd(2, '0')).toBe('00')
    expect('1'.padEnd(2, '0')).toBe('10')
    expect('01'.padEnd(2, '0')).toBe('01')

    expect(ResultsService.formatTrackScore(13.1)).toBe('13.10')
    expect(cleanUpScoreValueForDisplay(13.1)).toBe('13.1')
    expect(isNumeric('13.1')).toBe(true)

    expect(ResultsService.getScoreToDisplay(13.1, false)).toBe('13.10')
    expect(ResultsService.getScoreToDisplay(13.01, isTrackEvent)).toBe('13.01')
    expect(ResultsService.getScoreToDisplay(13.0, isTrackEvent)).toBe('13.00')
    expect(ResultsService.getScoreToDisplay(123.1, isTrackEvent)).toBe('123.10')
    expect(ResultsService.getScoreToDisplay('123.1', isTrackEvent)).toBe('123.10')

    expect(ResultsService.getScoreToDisplay('!#!123.1', isTrackEvent)).toBe('123.10')

    expect(ResultsService.getScoreToDisplay(599, true)).toBe('9:59.00')
  })

  it('getHighestScoreFromTrials', () => {
    expect(
      ResultsService.getHighestScoreFromTrials({
        t1: 10.1,
        t4: 10.8,
        t5: 10.5,
        t6: 10.6,
        t8: 10.4
      })
    ).toBe('10.8')

    expect(
      ResultsService.getHighestScoreFromTrials({
        t1: 10.1,
        t4: '!#!10.8',
        t5: 10.5,
        t6: 10.6,
        t8: 10.4
      })
    ).toBe('!#!10.8')
  })

  it('getEventTypesFromSchedule', () => {
    const data = simpleClone(mockCompData)
    const scheduledEvents: CompEvent[] = Object.values(data.events)
    expect(scheduledEvents.length).toBe(5)
    const result = ResultsService.getEventTypesFromSchedule(scheduledEvents)
    expect(result.T).toBe('Track')
    expect(result.F).toBe('Field')
  })

  it('filterLiveFeedShowOnlyFollowing', () => {
    let result
    const following = simpleClone(followingMock)
    const liveFeed = simpleClone(liveFeedMock)
    expect(liveFeed.length).toBe(20)
    result = ResultsService.filterLiveFeedShowOnlyFollowing(liveFeed, {})
    expect(result.length).toBe(20)

    result = ResultsService.filterLiveFeedShowOnlyFollowing(liveFeed, following)
    expect(result.length).toBe(18)

    /*
    {
      '9559': { id: 9559, name: 'Discus SB' },
      '9568': { id: 9568, name: 'High Jump IG' },
      '9578': { id: 9578, name: 'Javelin SG' }
    }
     */

    result = ResultsService.filterLiveFeedShowOnlyFollowing(liveFeed, {
      '9559': { id: 9559, name: 'Discus SB' }
    })
    expect(result.length).toBe(0)

    // expect(result.F).toBe('Field')
  })

  /*

getTrialsAbnormal Write some tests for the following and anything else you can think of

input
{
	"1.75:1": "O",
	"1.80:1": "x",
	"1.80:2": "x",
	"1.80:3": "O",
	"1.85:1": "O",
	"1.90:1": "O",
	"1.95:1": "O",
	"2.00:1": "X",
	"2.00:2": "O",
	"2.05:1": "O",
	"2.10:1": "O",
	"2.12:1": "O"
	"2.12:2": "-"
	"2.12:3": "W"
	"2.12:4": "T"
	"2.12:5": "F"
	"2.12:6": "O"
}

output
{
	"1.75": "O",
	"1.80": "xxO",
	"1.85": "O",
	"1.90": "O",
	"1.95": "O",
	"2.00": "X0",
	"2.05": "O",
	"2.10": "O",
	"2.12": "O-WTFO"
}
 */
  it('getTrialsAbnormal', () => {
    const trials = {
      '1.75:1': 'O',
      '1.80:1': 'x',
      '1.80:2': 'x',
      '1.80:3': 'O',
      '1.85:1': 'O',
      '1.90:1': 'O',
      '1.95:1': 'O',
      '2.00:1': 'X',
      '2.00:2': 'O',
      '2.05:1': 'O',
      '2.10:1': 'O',
      '2.12:1': 'O',
      '2.12:2': '-',
      '2.12:3': 'W',
      '2.12:4': 'T',
      '2.12:5': 'F',
      '2.12:6': 'O'
    }
    const result = ResultsService.getTrialsAbnormal(trials)
    expect(result['1.75']).toBe('O')
    expect(result['1.80']).toBe('xxO')
    expect(result['1.85']).toBe('O')
    expect(result['1.90']).toBe('O')
    expect(result['1.95']).toBe('O')
    expect(result['2.00']).toBe('XO')
    expect(result['2.05']).toBe('O')
    expect(result['2.10']).toBe('O')
    expect(result['2.12']).toBe('O-WTFO')
  })

  /**
   *
   */
  it('getEntriesGroupedByHeatNoNew', () => {
    const compOptions = { checkIn: { enabled: true } }
    const data = simpleClone(mockCompEventCheckIn)
    const result = ResultsService.getEntriesGroupedByHeatNoNew(compOptions, data)
    expect(Object.keys(result.entriesGroupedByHeatNo).length).toBe(1)
    expect(result.entriesGroupedByHeatNo['1'].length).toBe(2)
    expect(result.notEligible.length).toBe(6)
  })

  /**
   *
   */
  it('createQualifyToFromMap', () => {
    const data = simpleClone(mockScheduleForQualifyLinks)
    const result = ResultsService.createQualifyToFromMap(data)

    // T5: 13729 -> T52: 13730
    // T52: 13730 <- T5: 13729

    expect(result[13729].from).toBe(null)
    expect(result[13729].to!.egId).toBe('13730')
    expect(result[13729].to!.name).toBe('T52')

    expect(result[13730].from!.egId).toBe('13729')
    expect(result[13730].from!.name).toBe('T5')
    expect(result[13730].to).toBe(null)
  })

  // using mockScheduleForSorting test schedule sorting.
  it('sortSchedule', () => {
    const data = simpleClone(mockScheduleForSorting)
    const result = ResultsService.sortSchedule(data, 'eventNo')
    expect(result[0].typeNo).toBe('F1')
    expect(result[0].eventDate).toBe('2024-07-12T08:00:00.000Z')

    expect(result[1].typeNo).toBe('F2')
    expect(result[1].eventDate).toBe('2024-07-12T08:00:00.000Z')

    expect(result[2].typeNo).toBe('F3')
    expect(result[2].eventDate).toBe('2024-07-12T08:00:00.000Z')

    expect(result[3].typeNo).toBe('F4')
    expect(result[3].eventDate).toBe('2024-07-12T08:00:00.000Z')

    expect(result[4].typeNo).toBe('T1')
    expect(result[4].eventDate).toBe('2024-07-12T08:00:00.000Z')

    expect(result[5].typeNo).toBe('T2')
    expect(result[5].eventDate).toBe('2024-07-12T08:00:00.000Z')

    expect(result[6].typeNo).toBe('F5')
    expect(result[6].eventDate).toBe('2024-07-12T08:05:00.000Z')

    expect(result[7].typeNo).toBe('T3')
    expect(result[7].eventDate).toBe('2024-07-12T08:12:00.000Z')

    expect(result[8].typeNo).toBe('T4')
    expect(result[8].eventDate).toBe('2024-07-12T08:24:00.000Z')

    expect(result[9].typeNo).toBe('T5')
    expect(result[9].eventDate).toBe('2024-07-12T08:28:00.000Z')
  })
})
